package services.oneteam.ai.flow.event

import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import kotlinx.serialization.json.JsonArray
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.JsonPrimitive
import kotlinx.serialization.json.buildJsonObject
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import org.mockito.Mockito.mock
import org.mockito.Mockito.`when`
import services.oneteam.ai.flow.execution.*
import services.oneteam.ai.flow.execution.step.ContextToJsonObjectBuilder
import services.oneteam.ai.shared.domains.EntityMetadata
import services.oneteam.ai.shared.domains.collection.foundation.Foundation
import services.oneteam.ai.shared.domains.event.Event
import services.oneteam.ai.shared.domains.event.EventKey
import services.oneteam.ai.shared.domains.event.EventStatus
import services.oneteam.ai.shared.domains.flow.configuration.FlowConfiguration
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepType
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepType.Properties.Configuration
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepType.Properties.Configuration.TriggerEventSubscription
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepType.Properties.Configuration.TriggerEventSubscription.VariableMapping
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepTypeConfiguration
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepTypeConfigurationService
import services.oneteam.ai.shared.domains.workspace.Workspace
import services.oneteam.ai.shared.domains.workspace.WorkspaceVersion
import services.oneteam.ai.shared.domains.workspace.WorkspaceVersionService
import services.oneteam.ai.shared.otSerializer
import kotlin.test.Test
import kotlin.test.assertEquals


class TriggerFlowEventListenerTest {
    private val flowStepTypeConfigurationService = mock<FlowStepTypeConfigurationService>()
    private val workspaceVersionService = mock<WorkspaceVersionService>()
    private val flowExecutionService = mockFlowExecutionService()

    val triggerStepTypes = listOf(
        FlowStepTypeConfiguration(
            id = 1,
            type = "trigger",
            name = "Manual trigger from a form",
            description = "Trigger the flow manually from a button on a form in collection",
            tenantId = 1,
            primaryIdentifier = "manualTriggerFromForm",
            properties = FlowStepType.Properties(
                configuration = Configuration(
                    subscribeTo = mapOf(
                        EventKey.START_FLOW_MANUALLY_FROM_FORM to TriggerEventSubscription(
                            key = EventKey.START_FLOW_MANUALLY_FROM_FORM, condition = JsonObject(
                                mapOf(
                                    "AND" to JsonArray(
                                        listOf(
                                            JsonObject(
                                                mapOf(
                                                    "lhs" to JsonPrimitive("{{thisStep.buttonLabel}}"),
                                                    "operator" to JsonPrimitive("="),
                                                    "rhs" to JsonPrimitive("{{event.eventProperties.buttonLabel}}")
                                                )
                                            ), JsonObject(
                                                mapOf(
                                                    "lhs" to JsonPrimitive("{{event.eventProperties.form.formConfiguration.id}}"),
                                                    "operator" to JsonPrimitive("="),
                                                    "rhs" to JsonPrimitive("{{thisStep.formConfigurationId}}")
                                                )
                                            )
                                        )
                                    )
                                )
                            ), variableMappings = listOf(
                                VariableMapping(
                                    identifier = "{{thisStep.formVariableName}}",
                                    value = JsonPrimitive("{{event.eventProperties.form.id}}"),
                                    type = "form.{{thisStep.formConfigurationId}}",
                                )
                            )
                        )
                    )
                )
            )
        ), FlowStepTypeConfiguration(
            id = 2,
            type = "trigger",
            name = "Foundation created",
            description = "A trigger that executes when a foundation is created",
            tenantId = 1,
            primaryIdentifier = "foundationCreated",
            properties = FlowStepType.Properties(
                configuration = Configuration(
                    subscribeTo = mapOf(
                        EventKey.CREATE_COLLECTION_FOUNDATION to TriggerEventSubscription(
                            key = EventKey.CREATE_COLLECTION_FOUNDATION, condition = JsonObject(
                                mapOf(
                                    "lhs" to JsonPrimitive("{{event.eventProperties.foundation.foundationConfiguration.id}}"),
                                    "operator" to JsonPrimitive("="),
                                    "rhs" to JsonPrimitive("{{thisStep.foundationConfigurationId}}")
                                )
                            ), variableMappings = listOf(
                                VariableMapping(
                                    identifier = "{{thisStep.foundationVariableName}}",
                                    value = JsonPrimitive("{{event.eventProperties.foundation.id}}"),
                                    type = "foundation.{{thisStep.foundationConfigurationId}}",
                                )
                            )
                        )
                    )
                )
            ),
        ), FlowStepTypeConfiguration(
            id = 3,
            type = "trigger",
            name = "When this flow's webhook is received",
            description = "Trigger when webhook is received",
            tenantId = 1,
            primaryIdentifier = "receiveIncomingWebhookForFlow",
            properties = FlowStepType.Properties(
                configuration = Configuration(
                    subscribeTo = mapOf(
                        EventKey.RECEIVE_INCOMING_WEBHOOK_FLOW to TriggerEventSubscription(
                            key = EventKey.RECEIVE_INCOMING_WEBHOOK_FLOW, condition = JsonObject(
                                mapOf(
                                    "lhs" to JsonPrimitive("{{event.eventProperties.flowConfigurationId}}"),
                                    "operator" to JsonPrimitive("="),
                                    "rhs" to JsonPrimitive("{{global.flowConfigurationId}}")
                                )
                            ), variableMappings = listOf(
                                VariableMapping(
                                    identifier = "{{thisStep.payloadVariableName}}",
                                    value = JsonPrimitive("{{event.eventProperties.payload}}"),
                                    type = "json",
                                    properties = buildJsonObject {
                                        "schema" to JsonPrimitive("{{thisStep.expectedPayload.schema}}")
                                    })
                            )
                        )
                    )
                )
            )
        )
    )

    val flowConfigurations = listOf(
        FlowConfiguration.ForJson(
            id = FlowConfiguration.Id("1"),
            name = FlowConfiguration.Name("flow1"),
            description = FlowConfiguration.Description("flow1"),
            metadata = EntityMetadata.now(),
            start = FlowConfiguration.Step.Id("A"),
            triggers = mapOf(
                FlowConfiguration.Step.Id("A") to FlowConfiguration.Trigger(
                    id = FlowConfiguration.Step.Id("A"),
                    name = "Set Variables A",
                    properties = FlowConfiguration.Step.Properties(
                        typePrimaryIdentifier = "manualTriggerFromForm", inputs = mapOf(
                            "buttonLabel" to JsonPrimitive("Submit"),
                            "formConfigurationId" to JsonPrimitive("1231"),
                            "formVariableName" to JsonPrimitive("formVariableName1")
                        )
                    )
                ), FlowConfiguration.Step.Id("B") to FlowConfiguration.Trigger(
                    id = FlowConfiguration.Step.Id("B"),
                    name = "Set Variables B",
                    properties = FlowConfiguration.Step.Properties(
                        typePrimaryIdentifier = "manualTriggerFromForm", inputs = mapOf(
                            "buttonLabel" to JsonPrimitive("Submit"),
                            "formConfigurationId" to JsonPrimitive("formConfigurationId1"),
                            "formVariableName" to JsonPrimitive("formVariableName1")
                        )
                    )
                )
            )
        ), FlowConfiguration.ForJson(
            id = FlowConfiguration.Id("2"),
            name = FlowConfiguration.Name("flow2"),
            description = FlowConfiguration.Description("flow2"),
            metadata = EntityMetadata.now(),
            start = FlowConfiguration.Step.Id("C"),
            triggers = mapOf(
                FlowConfiguration.Step.Id("C") to FlowConfiguration.Trigger(
                    id = FlowConfiguration.Step.Id("C"),
                    name = "Set Variables C",
                    properties = FlowConfiguration.Step.Properties(
                        typePrimaryIdentifier = "foundationCreated", inputs = mapOf(
                            "buttonLabel" to JsonPrimitive("Submit"), "formConfigurationId" to JsonPrimitive("1232")
                        )
                    )
                ),
            )
        ), FlowConfiguration.ForJson(
            id = FlowConfiguration.Id("3"),
            name = FlowConfiguration.Name("webhook list schema"),
            description = FlowConfiguration.Description("webhook list schema"),
            metadata = EntityMetadata.now(),
            start = FlowConfiguration.Step.Id("A"),
            triggers = mapOf(
                FlowConfiguration.Step.Id("C") to FlowConfiguration.Trigger(
                    id = FlowConfiguration.Step.Id("C"),
                    name = "When this flow's webhook is received",
                    properties = FlowConfiguration.Step.Properties(
                        typePrimaryIdentifier = "receiveIncomingWebhookForFlow", inputs = mapOf(
                            "response" to JsonArray(
                                listOf(
                                    JsonObject(
                                        mapOf(
                                            "httpCode" to JsonPrimitive("200"), "expectedResponse" to JsonObject(
                                                mapOf(
                                                    "sample" to JsonObject(
                                                        mapOf(
                                                            "text_1" to JsonPrimitive("qwe"),
                                                            "number_1" to JsonPrimitive(123)
                                                        )
                                                    ), "schema" to JsonObject(
                                                        mapOf(
                                                            "id" to JsonPrimitive("aNQI1EexnRVf8r8TlkGGr"),
                                                            "text" to JsonPrimitive("JSON"),
                                                            "type" to JsonPrimitive("json"),
                                                            "identifier" to JsonPrimitive("aNQI1EexnRVf8r8TlkGGr"),
                                                            "properties" to JsonObject(
                                                                mapOf(
                                                                    "items" to JsonArray(
                                                                        listOf(
                                                                            JsonObject(
                                                                                mapOf(
                                                                                    "id" to JsonPrimitive("number_1"),
                                                                                    "text" to JsonPrimitive("number_1"),
                                                                                    "type" to JsonPrimitive("number"),
                                                                                    "identifier" to JsonPrimitive("number_1"),
                                                                                    "properties" to JsonObject(
                                                                                        mapOf(
                                                                                            "required" to JsonPrimitive(
                                                                                                false
                                                                                            )
                                                                                        )
                                                                                    )
                                                                                )
                                                                            ), JsonObject(
                                                                                mapOf(
                                                                                    "id" to JsonPrimitive("text_1"),
                                                                                    "text" to JsonPrimitive("text_1"),
                                                                                    "type" to JsonPrimitive("text"),
                                                                                    "identifier" to JsonPrimitive("text_1"),
                                                                                    "properties" to JsonObject(
                                                                                        mapOf(
                                                                                            "required" to JsonPrimitive(
                                                                                                false
                                                                                            )
                                                                                        )
                                                                                    )
                                                                                )
                                                                            )
                                                                        )
                                                                    )
                                                                )
                                                            )
                                                        )
                                                    )
                                                )
                                            )
                                        )
                                    ), JsonObject(
                                        mapOf(
                                            "httpCode" to JsonPrimitive("404")
                                        )
                                    )
                                )
                            ),
                            "expectedPayload" to JsonObject(
                                mapOf(
                                    "sample" to JsonArray(
                                        listOf(
                                            JsonObject(
                                                mapOf(
                                                    "form" to JsonObject(mapOf("id" to JsonPrimitive(17))),
                                                    "text_1" to JsonPrimitive("qwe"),
                                                    "number_1" to JsonPrimitive(123)
                                                )
                                            )
                                        )
                                    ), "schema" to JsonObject(
                                        mapOf(
                                            "id" to JsonPrimitive("aMkc0fCQ5us2m5zWXmwXf"),
                                            "text" to JsonPrimitive("List of JSON elements"),
                                            "type" to JsonPrimitive("list"),
                                            "identifier" to JsonPrimitive("aMkc0fCQ5us2m5zWXmwXf"),
                                            "properties" to JsonObject(
                                                mapOf(
                                                    "items" to JsonArray(
                                                        listOf(
                                                            JsonObject(
                                                                mapOf(
                                                                    "id" to JsonPrimitive("aMkc0fCQ5us2m5zWXmwXf"),
                                                                    "text" to JsonPrimitive("JSON"),
                                                                    "type" to JsonPrimitive("json"),
                                                                    "identifier" to JsonPrimitive("aMkc0fCQ5us2m5zWXmwXf")
                                                                )
                                                            )
                                                        )
                                                    )
                                                )
                                            )
                                        )
                                    )
                                )
                            ),
                            "payloadVariableName" to JsonPrimitive("incomingPayload"),
                            "continueFlowIfApiFails" to JsonPrimitive("false")
                        )
                    )
                )
            )
        )
    )

    val activeWorkspaceVersion = WorkspaceVersion.ForApi(
        id = WorkspaceVersion.Id(1),
        workspaceId = Workspace.Id(1),
        configuration = otSerializer.decodeFromString<Workspace.ForJson>(
            this::class.java.getResource("/sample-data/sgpit/sgpit-workspace-config.json")!!.readText()
        )
    )

    val triggerFlowEventListener = TriggerFlowEventListener(
        workspaceVersionService, flowStepTypeConfigurationService, flowExecutionService, false, timeoutMins = 2
    )

    @Test
    fun `test getSubscribedTriggerFlowStepTypeConfigurations`() = runTest {
        `when`(flowStepTypeConfigurationService.getAllByQuery(mapOf("type" to listOf("trigger")))).thenReturn(
            triggerStepTypes
        )

        val subscribedTriggerFlowStepTypeConfigurations: List<FlowStepTypeConfiguration> =
            triggerFlowEventListener.getSubscribedTriggerFlowStepTypeConfigurations(event)

        assertNotNull(subscribedTriggerFlowStepTypeConfigurations)
        assertEquals(
            listOf(triggerStepTypes[0]), subscribedTriggerFlowStepTypeConfigurations
        )

    }

    @Test
    fun `test getFlowsUsingSubscribedTriggerFlowStepType`() = runTest {
        `when`(flowStepTypeConfigurationService.getAllByQuery(mapOf("type" to listOf("trigger")))).thenReturn(
            triggerStepTypes
        )

        val subscribedTriggerFlowStepTypeConfigurations: List<FlowStepTypeConfiguration> =
            triggerFlowEventListener.getSubscribedTriggerFlowStepTypeConfigurations(event)

        val flowsUsingASubscribedTriggerFlowStepType: List<FlowsUsingASubscribedTriggerFlowStepType> =
            triggerFlowEventListener.getFlowsUsingSubscribedTriggerFlowStepType(
                flowConfigurations, subscribedTriggerFlowStepTypeConfigurations
            )

        assertNotNull(flowsUsingASubscribedTriggerFlowStepType)
        assertEquals(
            listOf(triggerStepTypes[0]), subscribedTriggerFlowStepTypeConfigurations
        )

    }

    data class Spec(
        val testDisplayName: String,
        val triggerId: FlowConfiguration.Step.Id,
        val flowConfigId: FlowConfiguration.Id,
        val triggerStepTypeId: Long,
        val event: Event.ForJson,
        val expected: Boolean
    )

    @ParameterizedTest
    @MethodSource("isMatchingConditionProvider")
    fun `test isMatchingCondition parameterized`(spec: Spec) = runTest {
        val flowConfig = flowConfigurations.first { it.id == spec.flowConfigId }
        val step = flowConfig.triggers?.get(spec.triggerId) as FlowConfiguration.Trigger
        val triggerStepType = triggerStepTypes.first { it.id == spec.triggerStepTypeId }

        val context = FlowContext(
            global = FlowContext.GlobalVariables(
                workspaceId = Workspace.Id(1),
                workspaceVersionId = WorkspaceVersion.Id(1),
                tenantId = 1,
                flowConfigurationId = flowConfig.id,
                flowConfigurationName = flowConfig.name
            ),
            workspace = FlowContext.WorkspaceContext(
                documentId = Workspace.DocumentId("1"),
                id = Workspace.Id(1),
                key = Workspace.Key("WORKSPACE1"),
                name = Workspace.Name("workspace1"),
                workspaceFoundationId = Foundation.Id(1),
                variables = emptyMap()
            ),
            event = Event.ForApi(
                spec.event.workspaceId,
                spec.event.eventProperties,
                spec.event.id,
                spec.event.tenantId,
            ),
        )
        val contextWithLocal = FlowContextWithLocalStep(
            stepId = FlowExecution.Step.Id(spec.triggerId.value),
            flowContext = context,
            thisStep = step.properties.inputs.mapValues { it.value }.toMutableMap()
        )

        val trigger = step.toExecution()
        val contextToJsonObjectBuilder = ContextToJsonObjectBuilder(emptyList(), trigger.id.value)
        TriggerTemplatePopulator(contextWithLocal, contextToJsonObjectBuilder).populateTrigger(
            trigger, triggerStepType.properties?.configuration!!
        )

        val result: Boolean = triggerFlowEventListener.isMatchingCondition(trigger.condition!!.toExpression())

        assertEquals(spec.expected, result)
    }

    companion object {

        val event = Event.ForJson(
            workspaceId = Workspace.Id(1), eventProperties = Event.EventProperties.StartFlowManuallyFromFormProperties(
                buttonLabel = "Submit",
                form = Event.FormMinimal(
                    10, 1,
                    formConfiguration = Event.FormConfigurationMinimal(
                        "formConfigurationId1", "formConfigurationKey1", "formName1", "serialId1"
                    ),
                    foundation = null,
                    documentId = "documentId1",
                    intervalId = "intervalId1",
                ),
                userId = 1,
            ), id = Event.Id("1"), tenantId = 1, status = EventStatus.QUEUED, entityMetadata = EntityMetadata.now()
        )

        val eventReceiveWebhookForFlow = Event.ForJson(
            workspaceId = Workspace.Id(1), eventProperties = Event.EventProperties.ReceiveIncomingWebhookForFlow(
                payload = JsonObject(
                    mapOf(
                        "number_1" to JsonPrimitive(123),
                        "text_1" to JsonPrimitive("qwe"),
                        "boolean_1" to JsonPrimitive(true),
                        "form" to JsonObject(
                            mapOf(
                                "identifier" to JsonPrimitive("form"), "id" to JsonPrimitive(252)
                            )
                        )
                    )
                ), flowConfigurationId = "3"
            ), id = Event.Id("2"), tenantId = 1, status = EventStatus.QUEUED, entityMetadata = EntityMetadata.now()
        )

        @JvmStatic
        fun isMatchingConditionProvider(): List<Spec> = listOf(
            Spec(
                testDisplayName = "test isMatchingCondition is false",
                triggerId = FlowConfiguration.Step.Id("A"),
                flowConfigId = FlowConfiguration.Id("1"),
                triggerStepTypeId = 1,
                event = event,
                expected = false
            ), Spec(
                testDisplayName = "test isMatchingCondition is true",
                triggerId = FlowConfiguration.Step.Id("B"),
                flowConfigId = FlowConfiguration.Id("1"),
                triggerStepTypeId = 1,
                event = event,
                expected = true
            ), Spec(
                testDisplayName = "test isMatchingCondition is true for receive webhook step type",
                triggerId = FlowConfiguration.Step.Id("C"),
                flowConfigId = FlowConfiguration.Id("3"),
                triggerStepTypeId = 3,
                event = eventReceiveWebhookForFlow,
                expected = true
            )
        )
    }

    @Test
    fun `test checkTriggerMatch should call triggerFlow when isMatchingCondition is true`() = runTest {
        triggerFlowEventListener.checkTriggerMatch(
            activeWorkspaceVersion, event, flowConfigurations[0], triggerStepTypes[0]
        )

        coVerify(exactly = 1) {
            flowExecutionService.createIfNoDuplicates(any(), any())
        }
    }
}

private fun mockFlowExecutionService(): FlowExecutionService {
    val flowExecutionForApi = FlowExecution.ForApi(
        id = FlowExecution.Id(1),
        workspaceId = Workspace.Id(1),
        status = FlowExecution.Status.COMPLETED,
        result = FlowExecution.Result.SUCCESS,
        documentId = FlowExecution.DocumentId("documentId1"),
        configurationId = "1",
        flowConfigurationName = "flow1"
    )
    val proxyService = mockk<FlowExecutionService>()
    coEvery { proxyService.createIfNoDuplicates(any(), any()) } returns flowExecutionForApi
    coEvery { proxyService.runFlowByExecutionId(any(), any()) } returns flowExecutionForApi
    return proxyService
}