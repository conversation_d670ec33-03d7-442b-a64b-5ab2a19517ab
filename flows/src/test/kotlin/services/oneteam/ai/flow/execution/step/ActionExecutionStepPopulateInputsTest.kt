package services.oneteam.ai.flow.execution.step

import kotlinx.coroutines.test.runTest
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.JsonPrimitive
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock
import services.oneteam.ai.flow.execution.FlowExecution.Step
import services.oneteam.ai.flow.execution.listeners.FlowListenerLogger
import services.oneteam.ai.shared.domains.flow.variables.VariableInstance
import services.oneteam.ai.shared.domains.VariableDataType
import services.oneteam.ai.shared.domains.actions.FilePressService
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepType
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepType.Properties.Configuration.Content.Properties
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepTypeConfiguration

class ActionExecutionStepPopulateInputsTest {

    @Test
    fun `should populate apiCall`() = runTest {

        // PREPARE
        val filePressService = mock(FilePressService::class.java)
        val proxyService = ProxyFixtures.mockForSuccess("OK")

        val flowStepTypeConfiguration = FlowStepTypeConfiguration(
            1, "myAction", "action", "My Action", null, FlowStepType.Properties(
                icon = FlowStepType.Properties.Icon("search"),
                isLocked = true,
                configuration = FlowStepType.Properties.Configuration(
                    apiCall = FlowStepType.Properties.Configuration.ApiCall(
                        url = "/ai/api/foundations/select-many",
                        body = JsonObject(mapOf()),
                        method = "POST",
                        internal = true,
                        response = FlowStepType.Properties.Configuration.ApiCall.Response(
                            type = "json",
                            properties = FlowStepType.Properties.Configuration.ApiCall.Response.Property(
                                items = listOf(
                                    FlowStepType.Properties.Configuration.ApiCall.Response.Item(
                                        type = "list",
                                        identifier = "foundations",
                                        properties = FlowStepType.Properties.Configuration.ApiCall.Response.Property(
                                            items = listOf(
                                                FlowStepType.Properties.Configuration.ApiCall.Response.Item(
                                                    type = "foundation.minimal",
                                                    identifier = "foundation"
                                                )
                                            )
                                        )
                                    )
                                )
                            )
                        )
                    ), variableMappings = listOf(), content = listOf(
                        FlowStepType.Properties.Configuration.Content(
                            text = "label1", type = "number", identifier = "x", properties = Properties()
                        ), FlowStepType.Properties.Configuration.Content(
                            text = "label2", type = "text", identifier = "y", properties = Properties()
                        ), FlowStepType.Properties.Configuration.Content(
                            text = "label3", type = "text", identifier = "z", properties = Properties()
                        )
                    )
                ),
            ), 1
        )

        val context = Fixtures.context()
        context.flowContext.listeners.add(FlowListenerLogger())
        context.flowContext.set(
            VariableInstance.Variable(
                JsonPrimitive("1"), VariableDataType.NUMBER, "a", null
            )
        )
        context.setThisStep("b", JsonPrimitive("y"))

        val mapBuilders = Fixtures.Context.buildMapBuilders()

        val step = ActionExecutionStep(
            Step(
                id = Step.Id("1"), name = "My Action", variant = Step.Variant.ACTION, properties = Step.Properties(
                    typePrimaryIdentifier = "myAction", variables = mutableListOf(), inputs = mutableMapOf(
                        "x" to JsonPrimitive("{{a}}"),
                        "y" to JsonPrimitive("{{thisStep.b}}"),
                        "z" to JsonPrimitive("{{thisStep.c}}")
                    )
                ), next = Step.Id("2")
            ),
            flowStepTypeConfiguration,
            proxyService = proxyService,
            internalProxyService = proxyService,
            filePressService = filePressService,
            contextToJsonObjectBuilder = ContextToJsonObjectBuilder(
                mapBuilders, Step.Id("1").value
            )
        )

        // PERFORM

        step.populate(context)

        // VERIFY

        // inputs should be copied into context thisStep with correct types

        assertThat(context.thisStep["x"]).isEqualTo(JsonPrimitive(1))
        assertThat(context.thisStep["y"]).isEqualTo(JsonPrimitive("y"))

    }

}
