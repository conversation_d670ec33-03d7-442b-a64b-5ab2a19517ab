package services.oneteam.ai.flow.execution

import kotlinx.coroutines.test.runTest
import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.JsonPrimitive
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock
import org.mockito.Mockito.`when`
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.flow.execution.mapBuilders.FoundationJsonMapBuilder
import services.oneteam.ai.shared.domains.EntityMetadata
import services.oneteam.ai.shared.domains.VariableDataType
import services.oneteam.ai.shared.domains.collection.foundation.Foundation
import services.oneteam.ai.shared.domains.collection.foundation.FoundationService
import services.oneteam.ai.shared.domains.flow.variables.VariableInstance
import services.oneteam.ai.shared.domains.workspace.FoundationConfiguration
import services.oneteam.ai.shared.domains.workspace.Workspace
import services.oneteam.ai.shared.otSerializer
import kotlin.test.assertEquals

class FoundationJsonMapBuilderTest {

    val logger: Logger = LoggerFactory.getLogger(javaClass)

    @Test
    fun `should build map`() = runTest {
        val foundationService = mock(FoundationService::class.java)
        val workspaceId = Workspace.Id(1)

        val workspace = otSerializer.decodeFromString<Workspace.ForJson>(
            this::class.java.getResource("/sample-data/sgpit/sgpit-workspace-config.json")!!.readText()
        )

        val foundation = Foundation.ForApi(
            Foundation.Id(4),
            Foundation.Name("foundationName"),
            Foundation.Key("foundationKey"),
            FoundationConfiguration.Id("emp"),
            workspaceId,
            Foundation.Id(5),
            EntityMetadata.now(),
            otSerializer.decodeFromString<JsonObject>("""{"prop": "value"}""")
        )

        `when`(foundationService.get(foundation.id)).thenReturn(foundation)


        val foundationJsonMapBuilder = FoundationJsonMapBuilder(
            foundationService, workspace
        )
        val formJsonMap = foundationJsonMapBuilder.handle(
            VariableInstance.Variable(
                JsonPrimitive(foundation.id.value), VariableDataType.fromString("foundation"), "foundationA"
            )
        )
        assertNotNull(formJsonMap)

        logger.debug(otSerializer.encodeToString(formJsonMap))

        val expected: JsonElement = otSerializer.decodeFromString<JsonElement>(
            """
{
  "id": 4,
  "name": "foundationName",
  "key": "foundationKey",
  "foundationConfigurationId": "emp",
  "foundationConfiguration": {
    "id": "emp",
    "name": "Employee",
    "relationship": "OneToMany",
    "description": "Employee"
  },
  "parentId": 5,
  "properties": {
    "prop": "value"
  }
}
            """.trimIndent()
        )

        assertEquals(expected, formJsonMap)
    }

}