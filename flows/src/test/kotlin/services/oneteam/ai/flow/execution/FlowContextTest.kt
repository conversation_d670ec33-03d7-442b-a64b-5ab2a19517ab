package services.oneteam.ai.flow.execution

import io.kotest.matchers.equals.shouldBeEqual
import kotlinx.coroutines.test.runTest
import kotlinx.serialization.json.JsonArray
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.JsonPrimitive
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import services.oneteam.ai.shared.domains.VariableDataType
import services.oneteam.ai.shared.domains.collection.foundation.Foundation
import services.oneteam.ai.shared.domains.event.Event
import services.oneteam.ai.shared.domains.event.Event.EventProperties.StartFlowManuallyFromFormProperties
import services.oneteam.ai.shared.domains.flow.configuration.FlowConfiguration
import services.oneteam.ai.shared.domains.flow.variables.VariableDefinition
import services.oneteam.ai.shared.domains.flow.variables.VariableInstance
import services.oneteam.ai.shared.domains.flow.variables.VariableProperties
import services.oneteam.ai.shared.domains.workspace.Workspace
import services.oneteam.ai.shared.domains.workspace.WorkspaceVersion
import services.oneteam.ai.shared.otSerializer

class FlowContextTest {

    val global = FlowContext.GlobalVariables(
        workspaceId = Workspace.Id(1),
        workspaceVersionId = WorkspaceVersion.Id(1),
        tenantId = 1,
        flowConfigurationId = FlowConfiguration.Id("1"),
        flowConfigurationName = FlowConfiguration.Name("flow")
    )

    val workspace = FlowContext.WorkspaceContext(
        documentId = Workspace.DocumentId("1"),
        id = Workspace.Id(1),
        key = Workspace.Key("WORKSPACE1"),
        name = Workspace.Name("workspace1"),
        workspaceFoundationId = Foundation.Id(1),
        variables = emptyMap()
    )

    val event = Event.ForApi(
        Workspace.Id(1), StartFlowManuallyFromFormProperties(
            buttonLabel = "string",
            form = null,
            userId = 1,
        ), Event.Id("1"), 1
    )

    @Test
    fun `should serialize context`() {
        val context = FlowContext(
            global = global, variables = mutableMapOf(
                "key" to VariableInstance.Variable(
                    value = JsonPrimitive("value"), type = VariableDataType.TEXT, identifier = "key", properties = null
                )
            ), workspace = workspace, event = event
        )
        val serialized = otSerializer.encodeToString(context)
        assertEquals(
            """
               {"global":{"workspaceId":1,"workspaceVersionId":1,"tenantId":1,"flowConfigurationId":"1","flowConfigurationName":"flow","flowDepth":1},"workspace":{"documentId":"1","id":1,"key":"WORKSPACE1","name":"workspace1","workspaceFoundationId":1,"variables":{}},"variables":{"key":{"variableType":"VARIABLE","value":"value","type":"TEXT","identifier":"key","properties":null}},"event":{"workspaceId":1,"eventProperties":{"buttonLabel":"string","form":null,"userId":1,"key":"START_flow_manually_from_form"},"id":"1","tenantId":1,"eventGroupId":null,"shouldNotifyUser":true}}
                """.trimIndent(), serialized
        )
    }

    @Test
    fun `should serialize context with list`() {
        val context = FlowContext(
            global = global, variables = mutableMapOf(
                "key" to VariableInstance.Variable(
                    value = JsonArray(listOf(JsonPrimitive("value"))),
                    type = VariableDataType.TEXT,
                    identifier = "key",
                    properties = null
                )
            ), workspace = workspace, event = event

        )

        val serialized = otSerializer.encodeToString(context)
        assertEquals(
            """
                {"global":{"workspaceId":1,"workspaceVersionId":1,"tenantId":1,"flowConfigurationId":"1","flowConfigurationName":"flow","flowDepth":1},"workspace":{"documentId":"1","id":1,"key":"WORKSPACE1","name":"workspace1","workspaceFoundationId":1,"variables":{}},"variables":{"key":{"variableType":"VARIABLE","value":["value"],"type":"TEXT","identifier":"key","properties":null}},"event":{"workspaceId":1,"eventProperties":{"buttonLabel":"string","form":null,"userId":1,"key":"START_flow_manually_from_form"},"id":"1","tenantId":1,"eventGroupId":null,"shouldNotifyUser":true}}
            """.trimIndent(), serialized
        )
    }

    @Test
    fun `should deserialize context`() {

        val serialized =
            """{"global":{"workspaceId":1,"workspaceVersionId":1,"tenantId":1,"flowConfigurationId":"1","flowConfigurationName":"flow"},"workspace":{"documentId":"1", "id":1, "key":"WORKSPACE1","name":"workspace1","workspaceFoundationId":1,"variables": {}},"variables":{"key":{"value":"value","type":"TEXT","identifier":"key","properties":null}},"event":{"workspaceId":1,"eventProperties":{"key":"START_flow_manually_from_form","buttonLabel":"string","form":null,"userId":1},"id":"1","tenantId":1}}"""
        val context: FlowContext = otSerializer.decodeFromString(serialized)
        assertEquals(
            FlowContext(
                global = global, variables = mutableMapOf(
                    "key" to VariableInstance.Variable(
                        value = JsonPrimitive("value"),
                        type = VariableDataType.TEXT,
                        identifier = "key",
                        properties = null
                    )
                ), workspace = workspace, event = event

            ), context
        )
    }

    @Test
    fun `should deserialize context with list`() {
        val serialized =
            """{"global":{"workspaceId":1,"workspaceVersionId":1,"tenantId":1,"flowConfigurationId":"1","flowConfigurationName":"flow"},"workspace":{"documentId":"1", "id":1, "key":"WORKSPACE1","name":"workspace1","workspaceFoundationId":1,"variables": {}},"variables":{"key":{"value":["value"],"type":"text","identifier":"key","properties":null}},"event":{"workspaceId":1,"eventProperties":{"key":"START_flow_manually_from_form","buttonLabel":"string","form":null,"userId":1},"id":"1","tenantId":1}}"""
        val context = otSerializer.decodeFromString(FlowContext.serializer(), serialized)
        assertEquals(
            FlowContext(
                global = global, variables = mutableMapOf(
                    "key" to VariableInstance.Variable(
                        value = JsonArray(listOf(JsonPrimitive("value"))),
                        type = VariableDataType.TEXT,
                        identifier = "key",
                        properties = null
                    )
                ), workspace = workspace, event = event

            ), context
        )
    }

    @Test
    fun `should be able to set value`() = runTest {
        val context = FlowContext(
            global = global, variables = mutableMapOf(
                "1" to VariableInstance.Variable(
                    value = JsonPrimitive("z"), type = VariableDataType.TEXT, identifier = "key", properties = null
                ), "2" to VariableInstance.Variable(
                    value = JsonPrimitive(9), type = VariableDataType.NUMBER, identifier = "key", properties = null
                )

            ), workspace = workspace, event = event

        )

        context.set(
            VariableInstance.Variable(
                value = JsonPrimitive("a"), type = VariableDataType.TEXT, identifier = "1", properties = null
            )
        )
        context.set(
            VariableInstance.Variable(
                value = JsonPrimitive(2), type = VariableDataType.NUMBER, identifier = "2", properties = null
            )
        )
        assertEquals(context.variables["1"]?.get(), JsonPrimitive("a"))
        assertEquals(context.variables["2"]?.get(), JsonPrimitive(2))
    }

    @Test
    fun `should map to object values`() = runTest {
        val context = FlowContext(
            global = global, variables = mutableMapOf(
                "form" to VariableInstance.Variable(
                    value = JsonObject(mapOf("id" to JsonPrimitive(400))),
                    identifier = "form",
                    type = VariableDataType.JSON,
                )
            ), workspace = workspace, event = event
        )

        assertEquals(JsonObject(mapOf("id" to JsonPrimitive(400))), context.variables["form"]?.get())
    }

    @Test
    fun `should deserialize json variable declaration`() = runTest {
        val serialized = """
              {
                "type": "json",
                "identifier": "document",
                "value": {
                  "id": "y5WOPTzFLI",
                  "url": "myUrl",
                  "path": "/upload/1/29/tmp/rsvZJocVvk-a2nJ5zCDagR4jqH9A8yRp94hD/y5WOPTzFLI",
                  "name": "outputdoc.docx",
                  "extension": "docx"
                },
                "properties": {
                  "items": [
                    { "type": "text", "identifier": "url" },
                    { "type": "text", "identifier": "id" },
                    { "type": "text", "identifier": "name" },
                    { "type": "text", "identifier": "path" },
                    { "type": "text", "identifier": "extension" }
                  ]
                }
              }
            """.trimIndent()
        val vm: VariableInstance.Variable = otSerializer.decodeFromString(serialized)
        vm.type shouldBeEqual VariableDataType.JSON
        vm.identifier shouldBeEqual "document"
        vm.get() shouldBeEqual JsonObject(
            mapOf(
                "id" to JsonPrimitive("y5WOPTzFLI"),
                "url" to JsonPrimitive("myUrl"),
                "path" to JsonPrimitive("/upload/1/29/tmp/rsvZJocVvk-a2nJ5zCDagR4jqH9A8yRp94hD/y5WOPTzFLI"),
                "name" to JsonPrimitive("outputdoc.docx"),
                "extension" to JsonPrimitive("docx")
            )
        )
        vm.properties?.shouldBeEqual(
            VariableProperties.JsonVariableProperties(
                items = listOf(
                    VariableDefinition.VariableConfiguration(
                        type = VariableDataType.TEXT, identifier = "url"
                    ), VariableDefinition.VariableConfiguration(
                        type = VariableDataType.TEXT, identifier = "id"
                    ), VariableDefinition.VariableConfiguration(
                        type = VariableDataType.TEXT, identifier = "name"
                    ), VariableDefinition.VariableConfiguration(
                        type = VariableDataType.TEXT, identifier = "path"
                    ), VariableDefinition.VariableConfiguration(
                        type = VariableDataType.TEXT, identifier = "extension"
                    )
                )
            )
        )
    }

}
