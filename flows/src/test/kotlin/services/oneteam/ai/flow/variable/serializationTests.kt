package services.oneteam.ai.flow.variable

import kotlinx.serialization.json.JsonPrimitive
import org.junit.jupiter.api.Test
import services.oneteam.ai.flow.execution.FlowContext
import services.oneteam.ai.shared.domains.VariableDataType
import services.oneteam.ai.shared.domains.event.Event
import services.oneteam.ai.shared.domains.event.Event.EventProperties.StartFlowManuallyFromFormProperties
import services.oneteam.ai.shared.domains.flow.configuration.FlowConfiguration
import services.oneteam.ai.shared.domains.flow.variables.VariableInstance
import services.oneteam.ai.shared.domains.workspace.Workspace
import services.oneteam.ai.shared.domains.workspace.WorkspaceVersion
import services.oneteam.ai.shared.otSerializer

class SerializationTests {

    val global = FlowContext.GlobalVariables(
        workspaceId = Workspace.Id(1),
        workspaceVersionId = WorkspaceVersion.Id(1),
        tenantId = 1,
        flowConfigurationId = FlowConfiguration.Id("1"),
        flowConfigurationName = FlowConfiguration.Name("flow")
    )

    val event = Event.ForApi(
        Workspace.Id(1), StartFlowManuallyFromFormProperties(
            buttonLabel = "string",
            form = null,
            userId = 1,
        ), Event.Id("1"), 1
    )

    @Test
    fun `should serialize variable`() {
        val variable = VariableInstance.Variable(
            JsonPrimitive("value"), VariableDataType.TEXT, "key", null
        )

        val serialized = otSerializer.encodeToString(variable)

        val deserialized = otSerializer.decodeFromString<VariableInstance>(serialized)

        assert(deserialized.get() == variable.get())
    }

    @Test
    fun `should serialize secured variable`() {

        val variable = VariableInstance.SecuredVariable(
            JsonPrimitive("value"), VariableDataType.TEXT, "key", null
        )

        val serialized = otSerializer.encodeToString(variable)

        val deserialized = otSerializer.decodeFromString<VariableInstance.SecuredVariable>(serialized)

        assert(deserialized.get() == variable.get())
    }

    @Test
    fun `should deserialize variable`() {
        val variable = VariableInstance.Variable(
            JsonPrimitive("value"), VariableDataType.TEXT, "key", null
        )

        val serialized = otSerializer.encodeToString(variable)

        val deserialized = otSerializer.decodeFromString<VariableInstance>(serialized)

        assert(deserialized.get() == variable.get())
    }

    @Test
    fun `should deserialize secured variable`() {
        val variable = VariableInstance.SecuredVariable(
            JsonPrimitive("value"), VariableDataType.TEXT, "key", null
        )

        val serialized = otSerializer.encodeToString(variable)

        val deserialized = otSerializer.decodeFromString<VariableInstance.SecuredVariable>(serialized)

        assert(deserialized.get() == variable.get())
    }
}