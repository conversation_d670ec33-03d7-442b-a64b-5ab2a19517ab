package services.oneteam.ai.flow.execution

import kotlinx.coroutines.test.runTest
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonPrimitive
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.flow.execution.listeners.DefaultListenerFactory
import services.oneteam.ai.flow.execution.listeners.FlowListenerLogger
import services.oneteam.ai.flow.execution.mapBuilders.FormJsonMapBuilder
import services.oneteam.ai.flow.execution.mapBuilders.FoundationJsonMapBuilder
import services.oneteam.ai.flow.execution.step.ExecutionStepFactoryV1
import services.oneteam.ai.shared.domains.EntityMetadata
import services.oneteam.ai.shared.domains.VariableDataType
import services.oneteam.ai.shared.domains.actions.FilePressService
import services.oneteam.ai.shared.domains.collection.form.Form
import services.oneteam.ai.shared.domains.collection.form.FormAnswer
import services.oneteam.ai.shared.domains.collection.form.FormAnswer.TextAnswer
import services.oneteam.ai.shared.domains.collection.form.FormService
import services.oneteam.ai.shared.domains.collection.foundation.Foundation
import services.oneteam.ai.shared.domains.collection.foundation.FoundationService
import services.oneteam.ai.shared.domains.event.Event
import services.oneteam.ai.shared.domains.event.Event.EventProperties.StartFlowManuallyFromFormProperties
import services.oneteam.ai.shared.domains.flow.configuration.FlowConfiguration
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepTypeConfigurationService
import services.oneteam.ai.shared.domains.flow.variables.VariableInstance
import services.oneteam.ai.shared.domains.proxy.ProxyService
import services.oneteam.ai.shared.domains.workspace.*
import services.oneteam.ai.shared.domains.workspace.document.create
import java.math.BigDecimal
import java.time.Instant
import kotlin.test.assertEquals

class FlowRunnerTest {
    val logger: Logger = LoggerFactory.getLogger(javaClass)
    val documentService = MockDocumentService()

    val event = Event.ForApi(
        Workspace.Id(1), StartFlowManuallyFromFormProperties(
            "string",
            null,
            null,
        ), Event.Id("1"), 1
    )

    @Test
    fun `test run flow`() = runTest {
        val flowConfiguration = FlowConfiguration.ForJson(
            id = FlowConfiguration.Id("flow-id"),
            name = FlowConfiguration.Name("flow-name"),
            description = FlowConfiguration.Description("flow-description"),
            metadata = EntityMetadata.now(),
            start = FlowConfiguration.Step.Id("A"),
            steps = mutableMapOf(
                FlowConfiguration.Step.Id("A") to FlowConfiguration.Step(
                    id = FlowConfiguration.Step.Id("A"),
                    name = "Set Variables A",
                    variant = FlowConfiguration.Step.Variant.SET_VARIABLES,
                    properties = FlowConfiguration.Step.Properties(
                        typePrimaryIdentifier = "type",
                        variables = listOf(
                            VariableInstance.Variable(
                                identifier = "partA",
                                value = JsonPrimitive("5"),
                                type = VariableDataType.NUMBER,
                                properties = null
                            )
                        ),
                    ),
                    next = FlowConfiguration.Step.Id("B")
                ),
                FlowConfiguration.Step.Id("B") to FlowConfiguration.Step(
                    id = FlowConfiguration.Step.Id("B"),
                    name = "Set Variables B",
                    variant = FlowConfiguration.Step.Variant.SET_VARIABLES,
                    properties = FlowConfiguration.Step.Properties(
                        typePrimaryIdentifier = "type",
                        variables = listOf(
                            VariableInstance.Variable(
                                identifier = "result",
                                value = JsonPrimitive("{{partA}} + 1"),
                                type = VariableDataType.NUMBER,
                                properties = null
                            )
                        ),
                    ),
                    next = FlowConfiguration.Step.Id("C")
                ),
                FlowConfiguration.Step.Id("C") to FlowConfiguration.Step(
                    id = FlowConfiguration.Step.Id("C"),
                    name = "Set Variables C",
                    variant = FlowConfiguration.Step.Variant.SET_VARIABLES,
                    properties = FlowConfiguration.Step.Properties(
                        typePrimaryIdentifier = "type",
                        variables = listOf(
                            VariableInstance.Variable(
                                identifier = "result",
                                value = JsonPrimitive("0.8 - 0.1"),
                                type = VariableDataType.NUMBER,
                                properties = null
                            )
                        ),
                    ),
                    next = FlowConfiguration.Step.Id("D")

                ),
                FlowConfiguration.Step.Id("D") to FlowConfiguration.Step(
                    id = FlowConfiguration.Step.Id("D"),
                    name = "Set Variables D",
                    variant = FlowConfiguration.Step.Variant.SET_VARIABLES,
                    properties = FlowConfiguration.Step.Properties(
                        typePrimaryIdentifier = "type",
                        variables = listOf(
                            VariableInstance.Variable(
                                identifier = "resultD",
                                value = JsonPrimitive("0.8"),
                                type = VariableDataType.NUMBER,
                                properties = null
                            )
                        ),
                    ),
                    next = FlowConfiguration.Step.Id("E")

                ),
                FlowConfiguration.Step.Id("E") to FlowConfiguration.Step(
                    id = FlowConfiguration.Step.Id("E"),
                    name = "Set Variables E",
                    variant = FlowConfiguration.Step.Variant.SET_VARIABLES,
                    properties = FlowConfiguration.Step.Properties(
                        typePrimaryIdentifier = "type",
                        variables = listOf(
                            VariableInstance.Variable(
                                identifier = "resultE",
                                value = JsonPrimitive("0.1"),
                                type = VariableDataType.NUMBER,
                                properties = null
                            )
                        ),
                    ),
                    next = FlowConfiguration.Step.Id("F")

                ),
                FlowConfiguration.Step.Id("F") to FlowConfiguration.Step(
                    id = FlowConfiguration.Step.Id("F"),
                    name = "Set Variables F",
                    variant = FlowConfiguration.Step.Variant.SET_VARIABLES,
                    properties = FlowConfiguration.Step.Properties(
                        typePrimaryIdentifier = "type",
                        variables = listOf(
                            VariableInstance.Variable(
                                identifier = "resultF",
                                value = JsonPrimitive("{{resultD}} - {{resultE}}"),
                                type = VariableDataType.NUMBER,
                                properties = null
                            )
                        ),
                    ),
                    next = FlowConfiguration.Step.Id("H")
                ),
                FlowConfiguration.Step.Id("H") to FlowConfiguration.Step(
                    id = FlowConfiguration.Step.Id("H"),
                    name = "Set Variables F using function",
                    variant = FlowConfiguration.Step.Variant.SET_VARIABLES,
                    properties = FlowConfiguration.Step.Properties(
                        typePrimaryIdentifier = "type",
                        variables = listOf(
                            VariableInstance.Variable(
                                identifier = "resultAVG",
                                value = JsonPrimitive("\$AVERAGE([{{resultD}}, {{resultE}}])"),
                                type = VariableDataType.NUMBER,
                                properties = null
                            )
                        ),
                    ),
                ),
            ),
            triggers = mapOf(
                FlowConfiguration.Step.Id("TRIGGER") to FlowConfiguration.Trigger(
                    id = FlowConfiguration.Step.Id("TRIGGER"),
                    name = "Trigger",
                    properties = FlowConfiguration.Step.Properties(
                        typePrimaryIdentifier = "type",
                        inputs = mapOf("key" to JsonPrimitive("value")),
                        variables = listOf(
                            VariableInstance.Variable(
                                identifier = "key",
                                value = JsonPrimitive("value"),
                                type = VariableDataType.TEXT,
                                properties = null
                            )
                        ),
                    )
                )
            ),
        )

        logger.debug("Using flow configuration {}", Json.encodeToString(flowConfiguration))

        val global = FlowContext.GlobalVariables(
            Workspace.Id(1),
            WorkspaceVersion.Id(1),
            1,
            FlowConfiguration.Id("flow-id"),
            FlowConfiguration.Name("flow-name"),
        )
        val workspaceContext = FlowContext.WorkspaceContext(
            documentId = Workspace.DocumentId("1"),
            id = Workspace.Id(1),
            key = Workspace.Key("WORKSPACE1"),
            name = Workspace.Name("workspace1"),
            workspaceFoundationId = Foundation.Id(1),
            variables = emptyMap()
        )
        val context = FlowContext(
            global = global, variables = mutableMapOf(), workspace = workspaceContext, event = event
        )

        val flow = flowConfiguration.toExecution(
            context, flowConfiguration.triggers?.values?.first()
        )

        val documentId = FlowExecution.DocumentId(
            documentService.create(
                1, FlowExecution.ForJson(
                    context,
                    flow.state,
                    flow.start,
                    flow.steps,
                )
            )
        )

        val formConfiguration = FormConfiguration.ForJson(
            id = "1", name = "Form", foundationId = "1", level = 1, metadata = EntityMetadata(
                createdAt = Instant.now(),
                updatedAt = Instant.now(),
            ), key = "key", content = listOf(
                BaseSection.TextQuestion(
                    id = BaseSection.Id("1"),
                    type = QuestionType.TEXT,
                    text = "How are you today?",
                    description = "description",
                    identifier = "identifier1",
                    properties = CommonQuestionProperties.TextQuestionProperties(
                        maxLength = 10, minLength = 1, required = true
                    )
                ), BaseSection.NumberQuestion(
                    id = BaseSection.Id("2"),
                    type = QuestionType.NUMBER,
                    text = "Bonus",
                    description = "description",
                    identifier = "Bonus",
                    properties = CommonQuestionProperties.NumberQuestionProperties(
                        decimalPlaces = 2,
                        min = BigDecimal(0),
                        max = BigDecimal(100000),
                        defaultValue = BigDecimal(0),
                        placeholder = "bonus",
                        type = CommonQuestionProperties.NumberType.NUMBER,
                        required = true
                    )
                )
            )
        )

        val formAnswers = FormAnswer.ForJson(
            Form.Id(1), mapOf(
                BaseSection.Id("1") to TextAnswer(BaseSection.Id("1"), "5"),
                BaseSection.Id("2") to FormAnswer.NumberAnswer(BaseSection.Id("2"), "500"),
            )
        )

        val workspace = Workspace.ForJson(
            Workspace.Id(1),
            Workspace.Name("1"),
            Workspace.Key("1"),
            Workspace.Description("1"),
            foundations = OrderedMap(listOf()),
            forms = mapOf(FormConfiguration.Id(formConfiguration.id) to formConfiguration),
            flows = OrderedMap(listOf(flowConfiguration)),
            series = mapOf(),
            labels = emptyMap(),
            variables = emptyMap(),
            EntityMetadata.now()
        )
        val flowExecutionId = 1.toLong()
        val blobStorageFEDRepository = mock(BlobStorageFEDRepository::class.java)
        val flowStepTypeConfigurationService = mock(FlowStepTypeConfigurationService::class.java)
        val flowExecutionRepository = mock(FlowExecutionRepository::class.java)
        val flowExecutionService = mock(FlowExecutionService::class.java)
        val formService = mock(FormService::class.java)
        val foundationService = mock(FoundationService::class.java)
        val proxyService = mock(ProxyService::class.java)
        val internalProxyService = mock(ProxyService::class.java)
        val workspaceVersionService = mock(WorkspaceVersionService::class.java)
        val filePressService = mock(FilePressService::class.java)

        val listenerFactory = DefaultListenerFactory(
            documentService,
            blobStorageFEDRepository,
            documentId = documentId,
            FlowExecution.Id(flowExecutionId),
            flowExecutionRepository,
            includeLogging = true,
            skipStepUpdates = false,
            skipVariableUpdates = false,
            skipSubFlowFlowUpdates = false,
            useAutomergeFED = false
        )
        val flowRunner = FlowRunner(
            FlowExecution.Id(flowExecutionId),
            context,
            flow,
            listenerFactory,
            listOf(FlowListenerLogger()),
            listOf(FlowListenerLogger()),
            flowExecutionService,
            timeoutMins = 2,
            ExecutionStepFactoryV1(
                flowStepTypeConfigurationService, proxyService, internalProxyService, filePressService, listOf(
                    FoundationJsonMapBuilder(foundationService, workspace), FormJsonMapBuilder(
                        formService, foundationService, documentService, null, workspace
                    ), DefaultMapBuilder() // must come last
                ), workspaceVersionService = workspaceVersionService
            )
        )

        val flowExecution = flowRunner.start()

        logger.debug("Document state: {}", documentService)
//            logger.debug(Json.encodeToString(documentService.documents))

        assertNotNull(flowExecution)

        // {"global":{"workspaceId":1,"workspaceVersionId":1,"tenantId":1,"flowConfigurationId":"flow-id","flowConfigurationName":"flow-name"},"variables":{"partA":{"value":5,"type":"int","identifier":"partA","properties":null},"result":{"value":0.7000000000000001,"type":"int","identifier":"result","properties":null},"resultD":{"value":0.8,"type":"double","identifier":"resultD","properties":null},"resultE":{"value":0.1,"type":"double","identifier":"resultE","properties":null},"resultF":{"value":0.7000000000000001,"type":"double","identifier":"resultF","properties":null},"resultAVG":{"value":0.45,"type":"double","identifier":"resultAVG","properties":null}}}
        assertEquals(JsonPrimitive(5), context.variables["partA"]?.get())
        assertEquals(JsonPrimitive(0.7000000000000001), context.variables["result"]?.get())
        assertEquals(JsonPrimitive(0.8), context.variables["resultD"]?.get())
        assertEquals(JsonPrimitive(0.1), context.variables["resultE"]?.get())
        assertEquals(JsonPrimitive(0.7000000000000001), context.variables["resultF"]?.get())
        assertEquals(JsonPrimitive(0.45), context.variables["resultAVG"]?.get())
    }

}

