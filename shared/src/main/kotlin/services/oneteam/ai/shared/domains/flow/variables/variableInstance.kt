package services.oneteam.ai.shared.domains.flow.variables

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.JsonPrimitive
import services.oneteam.ai.shared.domains.TypeToJsonElementConverter
import services.oneteam.ai.shared.domains.VariableDataType
import services.oneteam.ai.shared.domains.VariableDataTypeSerializer

val maskedValue = JsonPrimitive("****")

@Serializable
sealed class VariableInstance : Variable() {
    abstract fun toProperType(): JsonElement

    @Serializable
    @SerialName(VariableType.VARIABLE.name)
    data class Variable(
        private val value: JsonElement,
        @Serializable(with = VariableDataTypeSerializer::class) override val type: VariableDataType,
        override val identifier: VariableIdentifier,
        override val properties: VariableProperties? = null,
    ) : VariableInstance() {

        /**
         * Convert the variable to the proper type. We store numbers as strings, but when we want to use it as a number (e.g. in a calculation), we need to convert it to a number so it won't get quoted.
         */
        override fun toProperType(): JsonElement {
            return TypeToJsonElementConverter.convert(type, value)
        }

        override fun get(): JsonElement = value

        companion object Companion {
            fun of(
                value: JsonElement, type: VariableDataType, identifier: String, properties: VariableProperties? = null
            ): Variable {
                return Variable(value, type, identifier, properties)
            }

            fun of(
                value: Any, type: VariableDataType, identifier: String, properties: VariableProperties? = null
            ): Variable {
                return Variable(TypeToJsonElementConverter.toJsonElement(value), type, identifier, properties)
            }
        }
    }

    @Serializable
    @SerialName(VariableType.SECURED_VARIABLE.name)
    data class SecuredVariable(

        private val value: JsonElement,
        @Serializable(with = VariableDataTypeSerializer::class) override val type: VariableDataType,
        override val identifier: VariableIdentifier,
        override val properties: VariableProperties? = null,
    ) : VariableInstance() {

        /**
         * Convert the variable to the proper type. We store numbers as strings, but when we want to use it as a number (e.g. in a calculation), we need to convert it to a number so it won't get quoted.
         */
        override fun toProperType(): JsonElement {
            return TypeToJsonElementConverter.convert(type, value)
        }

        override fun get(): JsonElement = maskedValue //

        companion object Companion {
            fun of(
                value: JsonElement, type: VariableDataType, identifier: String, properties: VariableProperties? = null
            ): Variable {
                return Variable(value, type, identifier, properties)
            }

            fun of(
                value: Any, type: VariableDataType, identifier: String, properties: VariableProperties? = null
            ): Variable {
                return Variable(TypeToJsonElementConverter.toJsonElement(value), type, identifier, properties)
            }
        }
    }
}


