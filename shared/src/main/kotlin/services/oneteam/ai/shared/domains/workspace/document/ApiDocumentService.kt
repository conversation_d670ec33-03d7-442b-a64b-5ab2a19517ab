package services.oneteam.ai.shared.domains.workspace.document

import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import kotlinx.serialization.InternalSerializationApi
import kotlinx.serialization.KSerializer
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.buildJsonObject
import kotlinx.serialization.json.encodeToJsonElement
import kotlinx.serialization.serializer
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.shared.domains.collection.form.FormAlertRequestSyncServerBody
import services.oneteam.ai.shared.domains.collection.form.SetAnswerParams
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepTypeConfigurationService
import services.oneteam.ai.shared.domains.proxy.ProxyService
import services.oneteam.ai.shared.domains.proxy.ProxyService.ProxyEndpointBody
import services.oneteam.ai.shared.domains.proxy.useCookiesOrFallOverToServiceAccount
import services.oneteam.ai.shared.domains.workspace.CollaborationDocument
import services.oneteam.ai.shared.domains.workspace.CollaborationDocumentType
import services.oneteam.ai.shared.domains.workspace.validation.WorkspaceConfigValidatorBuilder
import services.oneteam.ai.shared.middlewares.RequestContext
import services.oneteam.ai.shared.otSerializer
import kotlin.coroutines.coroutineContext
import kotlin.reflect.KClass

@Serializable
data class DocumentCreationResponse(
    val documentId: String,
)

class ApiDocumentService(
    private val proxyService: ProxyService,
    stepTypeConfigurationService: FlowStepTypeConfigurationService
) : IDocumentService {
    val logger: Logger = LoggerFactory.getLogger(javaClass)
    val workspaceConfigValidatorBuilder = WorkspaceConfigValidatorBuilder(stepTypeConfigurationService)

    suspend fun getViewUrl(documentId: String): String {
        return ProxyService.buildTenantUrl(
            endpoint = "/ai/api/debug/#automerge:$documentId"
        )
    }

    @OptIn(InternalSerializationApi::class)
    override suspend fun <T : CollaborationDocument> create(
        tenantId: Long,
        document: T,
        type: KClass<T>,
        timeoutMillis: Long?
    ): String {
        logger.debug("create {}", document.descriptor())

        val requestPayload = ProxyEndpointBody(
            url = ProxyService.buildInternalSyncTenantUrl(endpoint = "/create/${tenantId}"),
            method = "POST",
            body = Json.encodeToJsonElement(type.serializer(), document),
        )

        val response = proxyService.call(
            requestPayload,
            isExternalResponse = false,
            timeoutMillis = timeoutMillis
        )

        if (response.status != ProxyService.ProxyEndpointResponseStatus.SUCCESS) {
            val errorMessage = "Failed to create document: ${response.error}"
            logger.error(errorMessage)
            throw Exception(errorMessage)
        }

        val documentCreation: DocumentCreationResponse = Json.decodeFromString(response.response as String)
        return documentCreation.documentId
    }

    @OptIn(InternalSerializationApi::class)
    override suspend fun <T : CollaborationDocument> show(
        id: String, cookie: String?, type: KClass<T>, _ignoreUnknownKeys: Boolean
    ): T {
        logger.debug("show {}", id)
        val serializer: KSerializer<T> = type.serializer()
//        val json = Json {
//            ignoreUnknownKeys = _ignoreUnknownKeys
//            encodeDefaults = true
//        }

        val requestPayload = ProxyEndpointBody(
            url = ProxyService.buildInternalSyncTenantUrl(endpoint = "/show/${id}"),
            method = "GET",
            body = null
        ).useCookiesOrFallOverToServiceAccount(cookie)

        val response = proxyService.call(
            requestPayload
        )

        if (response.status != ProxyService.ProxyEndpointResponseStatus.SUCCESS) {
            val errorMessage = "Failed to get document: ${response.error}"
            logger.error(errorMessage)
            throw Exception(errorMessage)
        }

        val body = response.response as String

        return otSerializer.decodeFromString(serializer, body)
    }

    @OptIn(InternalSerializationApi::class)
    override suspend fun <T : CollaborationDocument> update(
        id: String, cookie: String?, content: T, path: String, type: KClass<T>
    ): String {
        logger.debug("update {}", id)
        val requestPayload = ProxyEndpointBody(
            url = ProxyService.buildInternalSyncTenantUrl(endpoint = "/replace/${id}"),
            method = "PUT",
            body = buildJsonObject {
                put("value", Json.encodeToJsonElement(type.serializer(), content))
                put("path", Json.encodeToJsonElement(path))
            }
        ).useCookiesOrFallOverToServiceAccount(cookie)

        val response = proxyService.call(
            requestPayload
        )

        if (response.status != ProxyService.ProxyEndpointResponseStatus.SUCCESS) {
            val errorMessage = "Failed to get document: ${response.error}"
            logger.error(errorMessage)
            throw Exception(errorMessage)
        }

        return response.response as String
    }

    @OptIn(InternalSerializationApi::class)
    override suspend fun <T : Any> upsertAtPosition(
        documentId: String,
        cookie: String?,
        position: String,
        content: T,
        type: KClass<T>,
        updateDelegate: (T) -> Unit
    ) {
        logger.debug("upsertAtPosition {} , {} ", documentId, position)
        val tenant = coroutineContext[RequestContext]!!.tenant

        val requestPayload = ProxyEndpointBody(
            url = ProxyService.buildInternalSyncTenantUrl(endpoint = "/upsert/$documentId/$position"),
            method = "PUT",
            body = buildJsonObject {
                put(
                    "value", Json.encodeToJsonElement(type.serializer(), content)
                )
            }
        ).useCookiesOrFallOverToServiceAccount(cookie)

        val response = proxyService.call(
            requestPayload
        )

        logger.debug("Upsert response for document {} , {}", documentId, position)
        if (response.status != ProxyService.ProxyEndpointResponseStatus.SUCCESS) {
            val errorMessage = "Failed to upsert document: ${response.error}"
            logger.error(errorMessage)
            throw Exception(errorMessage)
        }
    }

    override suspend fun validate(document: String): ValidationResult {

        val objectMapper = ObjectMapper()
        val jsonDocument: JsonNode = objectMapper.readTree(document)
        // get type field from json document
        val documentType = jsonDocument["type"].asText("unknown")

        if (documentType == null) {
            logger.debug("No type field found in document - skipping validation")
            return ValidationResult(errors = emptyList())
        }

        // we expect a document with a "type" field which we can use to determine the validation context
        val type = CollaborationDocumentType.valueOf(documentType)

        // build a validation context based on the document type
        val validator = when (type) {
            CollaborationDocumentType.WORKSPACE_CONFIGURATION -> workspaceConfigValidatorBuilder.build(jsonDocument)
            CollaborationDocumentType.FLOW_EXECUTION,
            CollaborationDocumentType.FORM_ANSWER,
            CollaborationDocumentType.FORM_ANNOTATION -> null
        }

        if (validator == null) {
            logger.debug("No validator found for document type {} - skipping validation", type)
            return ValidationResult(errors = emptyList())
        }

        val assertions = validator.validate(jsonDocument).getErrors()

        val errors = assertions.map {
            Error(
                Field(it.key.value),
                Type(it.type.value),
                Path(it.path.value),
                ConstraintDetail(it.constraintDetail?.value),
                Message(it.message.value)
            )
        }

        return ValidationResult(errors = errors)
    }

    override suspend fun answerQuestion(
        documentId: String,
        cookie: String?,
        answers: List<SetAnswerParams>
    ): String {
        logger.debug("answerQuestion {}", documentId)

        val requestPayload = ProxyEndpointBody(
            url = ProxyService.buildInternalSyncTenantUrl(endpoint = "/document/${documentId}/form/answer"),
            method = "POST",
            body = Json.encodeToJsonElement(answers)
        ).useCookiesOrFallOverToServiceAccount(cookie)
        val response = proxyService.call(
            requestPayload
        )

        if (response.status != ProxyService.ProxyEndpointResponseStatus.SUCCESS) {
            val errorMessage = "Failed to set answer: ${response.error}"
            logger.error(errorMessage)
            throw Exception(errorMessage)
        }

        return response.response as String
    }

    override suspend fun alertAnnotation(
        annotationDocumentId: DocumentId,
        cookie: String?,
        alerts: List<FormAlertRequestSyncServerBody>
    ): String {
        logger.debug("alertAnnotation {}", annotationDocumentId)

        val requestPayload = ProxyEndpointBody(
            url = ProxyService.buildInternalSyncTenantUrl(endpoint = "/document/${annotationDocumentId}/form/alert"),
            method = "POST",
            body = Json.encodeToJsonElement(alerts)
        ).useCookiesOrFallOverToServiceAccount(cookie)
        val response = proxyService.call(
            requestPayload
        )

        if (response.status != ProxyService.ProxyEndpointResponseStatus.SUCCESS) {
            val errorMessage = "Failed to set alert: ${response.error}"
            logger.error(errorMessage)
            throw Exception(errorMessage)
        }

        return response.response as String
    }
}