package services.oneteam.ai.shared.domains.flow.variables

import kotlinx.serialization.json.Json
import kotlinx.serialization.json.encodeToJsonElement
import services.oneteam.ai.shared.domains.VariableDataType
import services.oneteam.ai.shared.domains.workspace.WorkspaceVariableConfiguration
import services.oneteam.ai.shared.domains.workspace.variable.WorkspaceVariable

//file mainly exists to provide translation functions for variables. workspace variables exist in the shared package. cant reference flow.variables from shared package without circular dependency.
//translation needs to occur here.

//would be super nice if the class hierachy reached up to the workspace variable, but we'd need to shuffle some packages around. will convert here for now :)
//"variable" will remain defined internally in the flow package. I belive it should be. it would make more sense to have "shared" depend on "flow". not the other way around. (treat flow like external library)

fun WorkspaceVariable.ForApi.toVariableInstance(): VariableInstance {
    return VariableInstance.Variable(
        value = Json.encodeToJsonElement(this.value),
        type = VariableDataType.TEXT,
        identifier = this.ref.ref,
        properties = null
    )
}

fun WorkspaceVariable.ForServiceFullyRevealed.toVariableInstance(): VariableInstance {
    return VariableInstance.Variable(
        value = Json.encodeToJsonElement(this.value),
        type = VariableDataType.TEXT,
        identifier = this.ref.ref,
        properties = null
    )
}

fun WorkspaceVariableConfiguration.ForApi.toVariableInstance(): VariableInstance {
    return VariableInstance.Variable(
        value = Json.encodeToJsonElement(this.value),
        type = VariableDataType.TEXT,
        identifier = this.name.value,
        properties = null
    )
}