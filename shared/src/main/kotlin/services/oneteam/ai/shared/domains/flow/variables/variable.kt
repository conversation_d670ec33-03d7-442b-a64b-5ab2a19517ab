package services.oneteam.ai.shared.domains.flow.variables

import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonClassDiscriminator
import kotlinx.serialization.json.JsonElement
import services.oneteam.ai.shared.domains.VariableDataType

@Serializable
@OptIn(ExperimentalSerializationApi::class)
@JsonClassDiscriminator("variableType")

sealed class Variable {
    abstract fun get(): JsonElement
    abstract val identifier: VariableIdentifier
    abstract val properties: VariableProperties?
    abstract val type: VariableDataType
}

typealias VariableIdentifier = String
