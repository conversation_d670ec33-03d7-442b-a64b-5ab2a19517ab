import React, { useMemo } from "react";

import {
  Box,
  Button,
  Card,
  CloseIconButton,
  Divider,
  FloatingWithParent,
  FloatingWithParentPosition,
  IconButton,
  Inline,
  Stack,
  Text
} from "@oneteam/onetheme";
import useOnClickOutside from "use-onclickoutside";

import { useAuth } from "@src/hooks/useAuth";
import { Annotation } from "@src/types/Annotation";

import { AnnotationDisplay } from "../AnnotationDisplay/AnnotationDisplay";
import { AnnotationMessageField } from "../AnnotationMessageField/AnnotationMessageField";
import { useFormAnnotations } from "../useFormAnnotations";
import "./FloatingAnnotations.scss";

export const FloatingAnnotations = ({
  parentRef,
  anchorName,
  onClose,
  location,
  annotations,
  onOpenInNew,
  onSave,
  canAddAnnotation = location?.variant !== "section",
  onlyAddingNewAnnotation = false,
  position = "right-bottom",
  hideLocationOnEachAnnotation
}: {
  parentRef: React.RefObject<HTMLElement>;
  anchorName: string;
  onClose: () => void;
  location?: Annotation["location"];
  annotations: Annotation[];
  onOpenInNew: () => void;
  onSave?: (message: string) => void;
  canAddAnnotation?: boolean;
  // Hide any pre-existing stuff
  onlyAddingNewAnnotation?: boolean;
  position?: `${FloatingWithParentPosition}`;
  // Only used for highlights
  hideLocationOnEachAnnotation?: boolean; // For when the name at the top of floating is always the same as all the annotations
}) => {
  console.log("FloatingAnnotations rendered", parentRef);
  const { user } = useAuth();
  const {
    onCreateAnnotation,
    onUpdateAnnotation,
    onDeleteAnnotation,
    onChangeResolveAnnotation,
    getAnnotationLocationDisplay
  } = useFormAnnotations();

  useOnClickOutside(parentRef, () => {
    onClose();
  });

  const annotationsToDisplay = useMemo(() => {
    if (annotations.length <= 4) {
      return annotations;
    }

    return annotations
      .slice(annotations.length - 4)
      .map(annotation => ({ ...annotation }));
  }, [annotations]);

  return (
    <FloatingWithParent
      className="floating-annotations"
      parentRef={parentRef}
      anchorName={anchorName}
      position={position}
      allowOutsideWindow
    >
      <Card
        size="small"
        style={{
          width: "300px"
        }}
      >
        <Stack onClick={e => e.stopPropagation()}>
          {!onlyAddingNewAnnotation && (
            <>
              <Inline
                gap="100"
                spaceBetween
                padding="100"
                key="floating-annotation-header"
                alignment="left"
              >
                <Box key="floating-annotation-header-left">
                  <Text
                    weight="regular"
                    color="text-secondary"
                    size="s"
                    alignment="left"
                  >
                    {getAnnotationLocationDisplay(location)}
                  </Text>
                </Box>
                <Inline key="floating-annotation-header-right" gap="100">
                  <IconButton
                    name="open_in_new"
                    color="text-tertiary"
                    onClick={onOpenInNew}
                  />
                  <CloseIconButton onClick={onClose} />
                </Inline>
              </Inline>
              {annotations.length > 0 && (
                <Divider
                  size="small"
                  key="floating-annotation-header-divider"
                />
              )}
              {annotations.length > 4 && (
                <>
                  <Box
                    alignment="left"
                    width="100"
                    style={{
                      padding: "4px 8px"
                    }}
                  >
                    <Button
                      key="floating-annotation-view-more"
                      size="small"
                      onClick={onOpenInNew}
                      variant="text"
                      label={`View more (${annotations.length - 4})`}
                    />
                  </Box>
                  <Divider size="small" />
                </>
              )}

              {annotationsToDisplay?.map((annotation, index) => (
                <React.Fragment key={`floating-annotation-${annotation.id}`}>
                  {index > 0 && (
                    <Divider
                      key={`floating-annotation-${annotation.id}-divider`}
                      size="small"
                    />
                  )}
                  <AnnotationDisplay
                    key={`floating-annotation-${annotation.id}`}
                    elementId={`floating-annotation-${annotation.id}`}
                    annotation={annotation}
                    user={user}
                    onCreateAnnotation={onCreateAnnotation}
                    onDeleteAnnotation={onDeleteAnnotation}
                    onUpdateAnnotation={onUpdateAnnotation}
                    onChangeResolveAnnotation={onChangeResolveAnnotation}
                    getAnnotationLocationDisplay={getAnnotationLocationDisplay}
                    hideAnnotationLocationDisplay={hideLocationOnEachAnnotation}
                  />
                </React.Fragment>
              ))}
            </>
          )}
          {onSave && canAddAnnotation && (
            <>
              <Divider size="small" key="floating-annotation-bottom-divider" />
              <Box padding="100" key="floating-annotation-bottom">
                <AnnotationMessageField
                  id="floating-annotation-message-field"
                  user={user}
                  onSave={onSave}
                />
              </Box>
            </>
          )}
        </Stack>
      </Card>
    </FloatingWithParent>
  );
};
