import React, { use<PERSON><PERSON>back, useEffect, useMemo } from "react";

import { reorderWithEdge } from "@atlaskit/pragmatic-drag-and-drop-hitbox/util/reorder-with-edge";
import {
  <PERSON>ert,
  AlertBackground,
  AlertIcon,
  AlertVariant,
  Box,
  Button,
  DragAndDrop,
  DraggableItem,
  IconButton,
  Inline,
  OnDrop,
  Stack
} from "@oneteam/onetheme";
import { get, set } from "lodash";

import {
  addItem,
  mapOverResource,
  removeItem
} from "@helpers/OrderedMapNoState";
import { getByPath } from "@helpers/automergeDocumentHelper.ts";

import { ReorderIcon } from "@components/shared/ReorderIcon";

import { useCollectionFormContext } from "@pages/collection/forms/CollectionFormContext";

import { Dictionary } from "@src/hooks/useDictionary.tsx";
import {
  AnswerLocation,
  TableCellLocation
} from "@src/types/AnnotationLocation";
import { Question } from "@src/types/Question.ts";
import { ListQuestionProperties } from "@src/types/QuestionProperties.ts";
import {
  FormAnswer,
  ListAnswer,
  ListAnswerItem
} from "@src/types/collection/CollectionForm";
import { FormAnswerDocument, Resource } from "@src/types/documentTypes.ts";

import { QuestionAnswer } from "./QuestionAnswer";
import { QuestionListHelper } from "./QuestionListHelper";

const getQuestionAnswers = (
  doc: FormAnswerDocument,
  accessor: string
): Resource<ListAnswerItem> | null => {
  try {
    return getByPath(doc, accessor.split("."));
  } catch (error) {
    console.error("Failed to get question answers:", error);
    return null;
  }
};

export const QuestionListAnswer = ({
  d,
  question,
  answer,
  answerAccessor,
  location,
  disableAddRow = false,
  highlightingOff = false,
  onPaste
}: {
  d: Dictionary;
  question: Question<ListQuestionProperties>;
  answer?: FormAnswer<ListAnswer>;
  answerAccessor: string;
  disableAddRow?: boolean;
  location?: AnswerLocation;
  highlightingOff?: boolean;
  onPaste?: (event: React.ClipboardEvent<HTMLInputElement>) => void;
}) => {
  const { docChange, getFieldState } = useCollectionFormContext();
  const rowId = (location as TableCellLocation)?.rowId;
  const baseTag = `list-properties-items-${question.id}`;
  const dropzoneTag = rowId ? `${baseTag}-${rowId}` : baseTag;

  const answerFieldState = getFieldState?.(`answers.${question?.id}`);

  const questionListHelper = useMemo(() => {
    return new QuestionListHelper(question);
  }, [question]);

  const createEmptyAnswerItem = useCallback(() => {
    return addItem(questionListHelper.createNewItem(), {
      entities: {},
      order: []
    });
  }, [questionListHelper]);

  useEffect(() => {
    // if there is no item on the list answer yet, create the first one
    if (docChange) {
      docChange(d => {
        const v = get(d, answerAccessor, undefined) as ListAnswer | undefined;
        if (!v?.order?.length && question.properties) {
          const answerPath = answerAccessor.split(".");
          // remove the last part of the path to get the base answer object
          answerPath.pop();

          const resource = createEmptyAnswerItem();
          set(d, answerPath, {
            questionId: question.id,
            value: resource,
            type: question.type
          });
        }
      });
    }
  }, [docChange, createEmptyAnswerItem, question, answerAccessor]);

  const listAnswer: ListAnswer = useMemo(() => {
    if (!docChange) {
      return createEmptyAnswerItem();
    }
    return (answer?.value ?? answer) as ListAnswer;
  }, [answer, createEmptyAnswerItem, docChange]);

  const itemQuestions = useMemo(() => {
    return question.properties?.items ?? [];
  }, [question.properties?.items]);

  const updateQuestionAnswers = useCallback(
    (action: (answers: Resource<ListAnswerItem>) => void) => {
      if (!docChange) {
        return;
      }

      docChange(doc => {
        const answers = getQuestionAnswers(doc, answerAccessor);
        if (!answers) {
          set(doc, answerAccessor, createEmptyAnswerItem());
        }
        if (answers) {
          action(answers);
        }
      });
    },
    [docChange, answerAccessor, createEmptyAnswerItem]
  );

  const addListRow = useCallback(() => {
    if (!question.properties) {
      return;
    }
    updateQuestionAnswers(answers => {
      addItem(questionListHelper.createNewItem(), answers);
    });
  }, [question.properties, updateQuestionAnswers, questionListHelper]);

  const removeListRow = useCallback(
    (accessor: string) => {
      updateQuestionAnswers(answers => {
        removeItem(questionListHelper.itemId(accessor), answers);
      });
    },
    [updateQuestionAnswers, questionListHelper]
  );

  const reorderItems: OnDrop = useCallback(
    ({ source, destination }) => {
      const reordered = reorderWithEdge({
        list: listAnswer?.order ?? [],
        startIndex: source.index,
        indexOfTarget: destination.index,
        closestEdgeOfTarget: destination.closestEdgeOfTarget,
        axis: "vertical"
      });

      updateQuestionAnswers(answers => {
        answers.order = reordered ?? [];
      });
    },
    [listAnswer?.order, updateQuestionAnswers]
  );

  const canAddMoreItems = useMemo(() => {
    const maxLength = question.properties?.maxLength;
    const currentLength = listAnswer?.order?.length ?? 0;

    if (maxLength || maxLength === 0) {
      return currentLength < maxLength;
    }
    return true;
  }, [question.properties?.maxLength, listAnswer?.order?.length]);

  return (
    <Stack
      className="select-properties-options"
      gap="050"
      width="100"
      alignment="left"
      style={{
        minWidth: "160px"
      }}
    >
      <DragAndDrop
        onDrop={reorderItems}
        dropzoneTags={[dropzoneTag]}
        className="select-properties-options__list"
      >
        {itemQuestions.length > 0 &&
          typeof listAnswer === "object" &&
          mapOverResource(listAnswer, (item, itemIndex) => {
            const itemAnswer = listAnswer?.entities[item.id]?.item[
              itemQuestions[0].id
            ]?.value as FormAnswer["value"];
            return (
              <DraggableItem
                index={itemIndex}
                key={item.id}
                canDropOver={data => {
                  return data.source.context.dropzoneTags.includes(dropzoneTag);
                }}
              >
                <Inline
                  className="select-properties-options__item"
                  width="100"
                  gap="050"
                  spaceBetween
                  key={item.id}
                >
                  {
                    <IconButton
                      name="do_not_disturb_on"
                      fillStyle="filled"
                      onClick={() =>
                        removeListRow(`${answerAccessor}.entities.${item.id}`)
                      }
                      disabled={!docChange || disableAddRow}
                      skipFocus
                    />
                  }
                  <Box
                    height="100"
                    width="100"
                    contentsHeight="100"
                    contentsWidth="fill"
                  >
                    <QuestionAnswer
                      key={`list-answer-field-${item.id}`}
                      question={itemQuestions[0]}
                      answer={itemAnswer}
                      answerAccessor={`${answerAccessor}.entities.${item.id}.item.${itemQuestions[0].id}.value`}
                      disabled={disableAddRow || question.properties?.disabled}
                      location={{
                        ...(location ?? {
                          variant: "answer",
                          questionId: question.id
                        }),
                        itemId: item.id
                      }}
                      // TODO: remove this when location for cells is fixed
                      highlightingOff={highlightingOff}
                      onPaste={onPaste}
                    />
                  </Box>
                  {listAnswer.order?.length > 1 && !disableAddRow && (
                    <ReorderIcon disabled={!docChange} />
                  )}
                </Inline>
              </DraggableItem>
            );
          })}
      </DragAndDrop>
      {docChange && canAddMoreItems && (
        <Button
          variant="text"
          onClick={() => {
            addListRow();
          }}
          disabled={disableAddRow}
          label={d("ui.configuration.forms.question.select.options.add")}
          leftIcon={{ name: "add_circle", fillStyle: "filled" }}
        />
      )}
      {answerFieldState?.error && (
        <Alert
          className="label__error"
          variant={AlertVariant.DANGER}
          background={AlertBackground.TRANSPARENT}
          icon={AlertIcon.NONE}
        >
          {answerFieldState?.error}
        </Alert>
      )}
    </Stack>
  );
};
