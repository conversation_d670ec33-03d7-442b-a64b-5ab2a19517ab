import React, { useEffect, useMemo, useState } from "react";

import { Box, Form, MultiSelectValue, Text } from "@oneteam/onetheme";
import { useFormContext } from "react-hook-form";

import { updateAnswer } from "@helpers/forms/answerHelper";

import { OTAIFormField } from "@components/shared/OTAIForm/OTAIFormField";

import { useCollectionFormContext } from "@pages/collection/forms/CollectionFormContext";

import { AnswerLocation } from "@src/types/AnnotationLocation";
import { Question, QuestionTypes } from "@src/types/Question.ts";
import {
  BooleanQuestionProperties,
  DateQuestionRangeValue,
  FilesQuestionProperties,
  TextQuestionProperties
} from "@src/types/QuestionProperties.ts";
import {
  AnswerDocChange,
  BooleanAnswer,
  FormAnswer,
  JsonAnswer,
  ListAnswer,
  TableAnswer
} from "@src/types/collection/CollectionForm.ts";

import { HighlightAnnotationLocation } from "../../HighlightAnnotationLocation/HighlightAnnotationLocation";

export const QuestionAnswer = ({
  question,
  answer: answerFromDoc,
  answerAccessor,
  disabled,
  location,
  highlightingOff = false,
  onPaste,
  onKeyDown,
  onFocus,
  onBlur,
  controlFocus
}: {
  question: Question;
  answer: FormAnswer["value"];
  answerAccessor: string;
  disabled?: boolean;
  location?: AnswerLocation;
  highlightingOff?: boolean;
  onPaste?: (event: React.ClipboardEvent<HTMLInputElement>) => void;
  onKeyDown?: (event: React.KeyboardEvent<HTMLInputElement>) => void;
  onFocus?: (event: React.FocusEvent<HTMLInputElement>) => void;
  onBlur?: (event: React.FocusEvent<HTMLInputElement>) => void;
  controlFocus?: boolean;
}) => {
  const { documentId, docChange } = useCollectionFormContext();
  // DO not pass the default value to answer for now, as we will use flows to set “default values” later
  // const shouldShowDefault = answer === undefined || answer === "";
  const shouldShowDefault = false;

  const { setValue, trigger } = useFormContext();

  const questionAnswerLocation: AnswerLocation = useMemo(() => {
    if (location) {
      return location;
    }
    return {
      variant: "answer",
      questionId: question.id
    };
  }, [location, question.id]);

  // While waiting for any answer to be updated, display the value it will be
  const [changeHappening, setChangeHappening] = React.useState<boolean>(false);
  const [answerToDisplay, setAnswerToDisplay] =
    useState<FormAnswer["value"]>(answerFromDoc);

  // Wait for answerFromDoc to match the pending answer before clearing changeHappening
  useEffect(() => {
    if (!changeHappening && answerFromDoc !== answerToDisplay) {
      // flow updates / other user updates / updateAnswer api fail
      setAnswerToDisplay(answerFromDoc);
    }
    if (changeHappening && answerFromDoc === answerToDisplay) {
      // if api successfully updated the answer, it can take some time for the doc to update
      // so we wait for the answerFromDoc to match the answerToDisplay; to avoid flickering
      setChangeHappening(false);
      setAnswerToDisplay(answerFromDoc);
    }
  }, [answerFromDoc, answerToDisplay, changeHappening]);

  const onChange = (
    question: Question,
    value: FormAnswer["value"],
    answerAccessor: string,
    docChange?: AnswerDocChange
  ) => {
    setChangeHappening(true);
    setAnswerToDisplay(value);
    if (!docChange) {
      if (!answerAccessor) {
        return;
      }
      setValue(answerAccessor, value);
    }
    trigger("answer");
    updateAnswer(question, value, answerAccessor, documentId).catch(error => {
      console.error("Error updating answer:", error);
      setChangeHappening(false); // this will reset value from doc in above useEffect
    });
  };

  if (question.type === QuestionTypes.JSON) {
    return <></>;
  }

  if (
    [
      QuestionTypes.TEXT,
      QuestionTypes.NUMBER,
      QuestionTypes.TEXT,
      QuestionTypes.DATE,
      QuestionTypes.SELECT,
      QuestionTypes.MULTISELECT,
      QuestionTypes.LIST,
      QuestionTypes.SCHEMA
    ]
      .map(String)
      .includes(question.type)
  ) {
    return (
      <Box
        width="100"
        alignment={question.type === QuestionTypes.NUMBER ? "right" : "left"}
        contentsWidth={
          disabled && question.type === QuestionTypes.TEXT ? "fit" : "100"
        }
      >
        {/* <HighlightAnnotationLocation
          location={questionAnswerLocation}
          width={
            disabled && question.type === QuestionTypes.TEXT ? "fit" : "100"
          }
          contentsWidth={
            disabled && question.type === QuestionTypes.TEXT ? "fit" : "100"
          }
          hugContainer
          disabled={!location || highlightingOff}
        > */}
        <OTAIFormField
          id={answerAccessor}
          question={question}
          answer={
            shouldShowDefault
              ? (question as Question<TextQuestionProperties>).properties
                  ?.defaultValue
              : answerToDisplay
          }
          isLabelExternal
          onChange={value => {
            onChange(
              question,
              value as
                | string
                | number
                | boolean
                | MultiSelectValue
                | DateQuestionRangeValue
                | TableAnswer
                | JsonAnswer
                | ListAnswer
                | undefined,
              answerAccessor,
              docChange
            );
          }}
          onPaste={onPaste}
          onKeyDown={onKeyDown}
          onFocus={onFocus}
          onBlur={onBlur}
          controlFocus={controlFocus}
          disabled={disabled}
          onlyTriggerChangeWhenBlur
        />
        {/* </HighlightAnnotationLocation> */}
      </Box>
    );
  }

  if (question.type === QuestionTypes.BOOLEAN) {
    const booleanQuestion = question as Question<BooleanQuestionProperties>;
    return (
      <HighlightAnnotationLocation
        width="100"
        contentsWidth="fill"
        hugContainer
        location={questionAnswerLocation}
      >
        <Form.RadioGroup
          id={answerAccessor}
          width="100"
          name={answerAccessor}
          options={[
            {
              value: true,
              label: booleanQuestion.properties?.trueText ?? "Yes"
            },
            {
              value: false,
              label: booleanQuestion.properties?.falseText ?? "No"
            }
          ]}
          onChange={value => {
            onChange(
              question,
              value as BooleanAnswer,
              answerAccessor,
              docChange
            );
          }}
          onPaste={onPaste}
          value={
            shouldShowDefault
              ? (booleanQuestion.properties?.defaultValue ?? undefined)
              : (answerToDisplay as boolean | undefined)
          }
          disabled={disabled}
        />
      </HighlightAnnotationLocation>
    );
  }

  if (question.type === QuestionTypes.FILES) {
    const files = question as Question<FilesQuestionProperties>;
    return (
      <HighlightAnnotationLocation
        location={questionAnswerLocation}
        width="100"
        contentsWidth="100"
        hugContainer
        // disabled={highlightingOff}
      >
        <OTAIFormField
          id={answerAccessor}
          isLabelExternal={true}
          question={files}
          answer={answerToDisplay}
          onChange={data => {
            setChangeHappening(true);
            const change = data as {
              op: string;
              file: { path: string; name: string };
            };

            const value = [];
            if (change.op === "ADD") {
              value.push(change.file);
            } else if (change.op === "DELETE") {
              //set name to undefined to indicate that the file needs to be deleted
              value.push({ path: change.file.path, name: "undefined" });
            }

            updateAnswer(question, value, answerAccessor, documentId).finally(
              () => {
                setChangeHappening(false);
              }
            );
          }}
          disabled={disabled}
        />
      </HighlightAnnotationLocation>
    );
  }

  if (question.type === QuestionTypes.TABLE) {
    return (
      <OTAIFormField
        id={answerAccessor}
        question={question}
        answer={
          shouldShowDefault
            ? (question as Question<TextQuestionProperties>).properties
                ?.defaultValue
            : answerToDisplay
        }
        isLabelExternal
        onChange={value => {
          onChange(
            question,
            value as
              | string
              | number
              | boolean
              | MultiSelectValue
              | DateQuestionRangeValue
              | TableAnswer
              | JsonAnswer
              | ListAnswer
              | undefined,
            answerAccessor,
            docChange
          );
        }}
        onPaste={onPaste}
        onKeyDown={onKeyDown}
        onFocus={onFocus}
        onBlur={onBlur}
        controlFocus={controlFocus}
        disabled={disabled}
        onlyTriggerChangeWhenBlur
      />
    );
  }

  return (
    <Text>
      {question.type} is not supported yet. Please contact the support team.
    </Text>
  );
};

QuestionAnswer.displayName = "QuestionAnswer";
