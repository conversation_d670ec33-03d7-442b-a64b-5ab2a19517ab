import React, { useCallback, useMemo, useRef, useState } from "react";

import {
  Box,
  FloatingWithParentPosition,
  IconButton,
  Inline,
  StatusCircleGroup,
  StatusCircleVariant,
  getClassNames,
  returnStringIfTrue
} from "@oneteam/onetheme";

import { getQuestionsInSection } from "@helpers/configurationFormHelper";

import { useCollectionFormContext } from "@pages/collection/forms/CollectionFormContext";
import { sortAnnotations } from "@pages/collection/forms/annotations/annotationHelpers";
import { useFormAnnotations } from "@pages/collection/forms/annotations/useFormAnnotations";

import { useAuth } from "@src/hooks/useAuth";
import {
  AnnotationResolvedStatus,
  AnnotationVariant
} from "@src/types/Annotation";
import {
  QuestionLocation,
  SectionLocation,
  TableCellLocation
} from "@src/types/AnnotationLocation";
import { Section } from "@src/types/FormConfiguration";
import { CollectionFormSummaryModalTab } from "@src/types/collection/CollectionForm";

import { FloatingAnnotations } from "../../../annotations/FloatingAnnotations/FloatingAnnotations";
import "./CollectionQuestionBlockComment.scss";

export const CollectionQuestionBlockComment = ({
  location,
  stylingVariant = "default",
  floatingAnnotationPosition = "bottom-left"
}: {
  location: SectionLocation | QuestionLocation;
  stylingVariant?: "default" | "cellIndicator";
  floatingAnnotationPosition?: `${FloatingWithParentPosition}`;
}) => {
  const { user } = useAuth();
  const parentRef = useRef<HTMLDivElement>(null);
  const [isOpen, setIsOpen] = useState(false);
  const anchorName = useMemo(() => {
    if (location.variant === "question") {
      return `question-${location.questionId}-${(location as TableCellLocation).rowId}-${(location as TableCellLocation).columnId}-comment`;
    }
    return `section-${location.sectionId}-comment`;
  }, [location]);

  const { formData, summaryModal, formAnnotationDocument } =
    useCollectionFormContext();
  const { onCreateAnnotation } = useFormAnnotations();

  const relevantAnnotations = useMemo(() => {
    let relevantAnnotations = Object.values(
      formAnnotationDocument?.annotations ?? {}
    ).filter(
      annotation =>
        annotation.variant === AnnotationVariant.COMMENT &&
        annotation.resolved?.status !== AnnotationResolvedStatus.RESOLVED
    );

    if (location.variant === "question" && location.questionId) {
      relevantAnnotations = relevantAnnotations.filter(
        annotation =>
          annotation.location?.variant === "question" &&
          annotation.location?.questionId === location.questionId &&
          ((!(location as TableCellLocation).columnId &&
            !(location as TableCellLocation).rowId) ||
            ((annotation.location as TableCellLocation)?.columnId ===
              (location as TableCellLocation).columnId &&
              (annotation.location as TableCellLocation)?.rowId ===
                (location as TableCellLocation).rowId))
      );
    } else if (location.variant === "section" && location.sectionId) {
      const questionIdsInSection: string[] = getQuestionsInSection(
        formData?.configuration.content.find(
          section => section.id === location.sectionId
        ) as Section
      ).map(question => question.id);
      relevantAnnotations = relevantAnnotations.filter(
        annotation =>
          annotation.location?.variant === "question" &&
          questionIdsInSection.includes(annotation.location.questionId)
      );
    }
    return sortAnnotations(relevantAnnotations);
  }, [
    formAnnotationDocument?.annotations,
    formData?.configuration.content,
    location
  ]);

  const iconDisplay = useMemo(() => {
    if (stylingVariant === "cellIndicator") {
      return (
        <Box
          className={getClassNames([
            "collection-question-block-comment__cell-indicator",
            relevantAnnotations.length > 0
              ? "collection-question-block-comment__cell-indicator--with-annotations"
              : "collection-question-block-comment__cell-indicator--without-annotations",
            returnStringIfTrue(
              isOpen,
              "collection-question-block-comment__cell-indicator--open"
            )
          ])}
        ></Box>
      );
    }
    if (relevantAnnotations.length > 0) {
      return (
        <StatusCircleGroup
          className={getClassNames([
            "collection-question-block-alert__icon",
            "collection-question-block-alert__icon--with-annotations"
          ])}
          options={[
            {
              variant: StatusCircleVariant.COMMENT,
              count: relevantAnnotations.length
            }
          ]}
        />
      );
    }
    return (
      <Inline
        className="collection-question-block-comment__icon"
        position="relative"
        alignment="center"
        padding="025"
        gap="025"
      >
        <IconButton
          name="add_comment"
          color={isOpen ? "color" : "text-tertiary"}
          size="m"
          skipFocus
        />
      </Inline>
    );
  }, [stylingVariant, relevantAnnotations.length, isOpen]);

  const onSaveAnnotation = useCallback(
    (message: string) => {
      if (!message || !user?.id) {
        return;
      }
      const newAnnotationId = onCreateAnnotation({
        annotation: {
          variant: AnnotationVariant.COMMENT,
          message,
          attachments: [],
          location
        }
      });
      setTimeout(() => {
        document
          .getElementById(`annotation-${newAnnotationId}`)
          ?.scrollIntoView({
            behavior: "smooth",
            block: "nearest",
            inline: "nearest"
          });
      }, 100);
    },
    [user, onCreateAnnotation, location]
  );

  if (relevantAnnotations.length === 0 && location.variant === "section") {
    return <></>;
  }

  return (
    <Box
      classNames={[
        "collection-question-block-comment__container",
        `collection-question-block-comment__container--${stylingVariant}`
      ]}
      ref={parentRef}
      style={{
        ["anchorName" as string]: `--${anchorName}`
      }}
    >
      <Box
        classNames={[
          "collection-question-block-comment",
          returnStringIfTrue(
            relevantAnnotations.length > 0,
            "collection-question-block-comment--with-annotations"
          ),
          returnStringIfTrue(isOpen, "collection-question-block-comment--open")
        ]}
        onClick={e => {
          setIsOpen(current => !current);
          e.stopPropagation();
          e.preventDefault();
        }}
      >
        {iconDisplay}
        {isOpen && (
          <FloatingAnnotations
            parentRef={parentRef}
            anchorName={anchorName}
            onClose={() => setIsOpen(false)}
            location={location}
            annotations={relevantAnnotations}
            onOpenInNew={() => {
              summaryModal.setTab(CollectionFormSummaryModalTab.COMMENTS);
              summaryModal.setLocation(location);
              setIsOpen(false);
            }}
            onSave={onSaveAnnotation}
            position={floatingAnnotationPosition}
          />
        )}
      </Box>
    </Box>
  );
};
