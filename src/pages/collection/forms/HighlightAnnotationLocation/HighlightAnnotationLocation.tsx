import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState
} from "react";

import {
  Badge,
  Box,
  FermionProps,
  FloatingWithParent,
  Icon,
  Inline,
  getClassNames,
  returnStringIfTrue
} from "@oneteam/onetheme";
import { set } from "lodash";

import { useAuth } from "@src/hooks/useAuth";
import { UserActor } from "@src/types/Actor";
import {
  AnnotationResolvedStatus,
  AnnotationVariant,
  HighlightAnnotation
} from "@src/types/Annotation";
import {
  AnswerLocation,
  HighlightColor,
  JsonOrListItemLocation,
  QuestionLocation,
  SectionLocation,
  TableCellLocation
} from "@src/types/AnnotationLocation";
import {
  CollectionFormMode,
  CollectionFormSummaryModalTab
} from "@src/types/collection/CollectionForm";

import { useCollectionFormContext } from "../CollectionFormContext";
import { FloatingAnnotations } from "../annotations/FloatingAnnotations/FloatingAnnotations";
import { sortAnnotations } from "../annotations/annotationHelpers";
import { useFormAnnotations } from "../annotations/useFormAnnotations";
import "./HighlightAnnotationLocation.scss";

export const HighlightAnnotationLocation = ({
  location,
  hugContainer = false,
  width = "fit",
  contentsWidth,
  disabled = !location,
  children
}: {
  location?: SectionLocation | QuestionLocation | AnswerLocation;
  hugContainer?: boolean;
  width?: FermionProps["width"];
  contentsWidth?: FermionProps["contentsWidth"];
  disabled?: boolean;
  children: React.ReactNode;
}) => {
  const { user } = useAuth();
  const parentRef = useRef<HTMLDivElement>(null);
  const [isOpen, setIsOpen] = useState(false);
  const { mode, summaryModal, formAnnotationDocument, currentHighlightColor } =
    useCollectionFormContext();
  const { onCreateAnnotation } = useFormAnnotations();

  const relevantAnnotations: HighlightAnnotation[] = useMemo(() => {
    let relevantAnnotations = Object.values(
      formAnnotationDocument?.annotations ?? {}
    ).filter(
      annotation =>
        annotation.variant === AnnotationVariant.HIGHLIGHT &&
        annotation.resolved?.status !== AnnotationResolvedStatus.RESOLVED
    ) as HighlightAnnotation[];
    if (!location) {
      return [];
    }

    if (
      (location.variant === "question" || location.variant === "answer") &&
      location.questionId
    ) {
      relevantAnnotations = relevantAnnotations.filter(annotation => {
        if (
          annotation.location?.variant !== location.variant ||
          annotation.location?.questionId !== location.questionId
        ) {
          return false;
        }
        const rowIdMatch = (location as TableCellLocation).rowId
          ? (location as TableCellLocation).rowId ===
            (annotation.location as TableCellLocation)?.rowId
          : !(annotation.location as TableCellLocation)?.rowId;

        const columnIdMatch = (location as TableCellLocation).columnId
          ? (location as TableCellLocation).columnId ===
            (annotation.location as TableCellLocation)?.columnId
          : !(annotation.location as TableCellLocation)?.columnId;

        const itemIdMatch = (location as JsonOrListItemLocation).itemId
          ? (location as JsonOrListItemLocation).itemId ===
            (annotation.location as JsonOrListItemLocation)?.itemId
          : !(annotation.location as JsonOrListItemLocation)?.itemId;

        return rowIdMatch && columnIdMatch && itemIdMatch;
      });
    } else if (location.variant === "section" && location.sectionId) {
      relevantAnnotations = relevantAnnotations.filter(
        annotation =>
          annotation.location?.variant === "section" &&
          annotation.location?.sectionId === location.sectionId
      );
    } else if (location.variant === "answer") {
      relevantAnnotations = relevantAnnotations.filter(
        annotation =>
          annotation.location?.variant === "answer" &&
          annotation.location?.answer?.documentVersionId ===
            location.answer?.documentVersionId &&
          annotation.location?.questionId === location.questionId
      );
    }
    return sortAnnotations(relevantAnnotations) as HighlightAnnotation[];
  }, [formAnnotationDocument?.annotations, location]);

  const anchorName = useMemo(() => {
    if (!location) {
      return "";
    } else if (location.variant === "section") {
      return `section-${location.sectionId}-highlight`;
    }
    const uniqueName = `${(location as QuestionLocation | AnswerLocation).questionId}-${(location as TableCellLocation).rowId}-${(location as TableCellLocation).columnId}-${(location as JsonOrListItemLocation).itemId}`;
    if (location.variant === "question") {
      return `question-${uniqueName}-highlight`;
    }
    return `answer-${uniqueName}-${location.answer?.documentVersionId}-highlight`;
  }, [location]);

  const [isHovering, setIsHovering] = React.useState(false);

  const mostRecentAnnotation = useMemo(
    () =>
      relevantAnnotations
        ? relevantAnnotations[relevantAnnotations.length - 1]
        : undefined,
    [relevantAnnotations]
  );

  const [temporaryHighlightColor, setTemporaryHighlightColor] = useState<
    HighlightColor | undefined
  >(undefined);

  const onSaveAnnotation = useCallback(
    (message: string) => {
      if (!user?.id) {
        return;
      }
      if (
        // Don't add another highlight if the most recent annotation is the same color, same user, and no message
        mostRecentAnnotation &&
        !message &&
        !mostRecentAnnotation.message &&
        mostRecentAnnotation.color ===
          (temporaryHighlightColor ?? currentHighlightColor) &&
        (mostRecentAnnotation.createdBy as UserActor)?.userId === user.id
      ) {
        setTemporaryHighlightColor(undefined);
        return;
      }

      const newAnnotationId = onCreateAnnotation({
        annotation: {
          variant: AnnotationVariant.HIGHLIGHT,
          message,
          attachments: [],
          location,
          color: temporaryHighlightColor ?? currentHighlightColor
        }
      });
      setTimeout(() => {
        document
          .getElementById(`annotation-${newAnnotationId}`)
          ?.scrollIntoView({
            behavior: "smooth",
            block: "nearest",
            inline: "nearest"
          });
        setTemporaryHighlightColor(undefined);
      }, 100);
    },
    [
      user?.id,
      mostRecentAnnotation,
      temporaryHighlightColor,
      currentHighlightColor,
      onCreateAnnotation,
      location
    ]
  );

  const handleClose = useCallback(() => {
    setIsOpen(false);
    if (temporaryHighlightColor) {
      onSaveAnnotation("");
    }
    setTemporaryHighlightColor(undefined);
    setIsHovering(false);
  }, [onSaveAnnotation, temporaryHighlightColor]);

  useEffect(() => {
    // For when we delete or resolve annotation
    if (isOpen && !relevantAnnotations.length && !temporaryHighlightColor) {
      setIsOpen(false);
    }
  }, [isOpen, relevantAnnotations.length, temporaryHighlightColor]);

  if (!location) {
    return children;
  }

  return (
    <Box
      classNames={[
        "highlight-location",
        returnStringIfTrue(
          temporaryHighlightColor || !!mostRecentAnnotation,
          getClassNames([
            "highlight-location--highlighted",
            `highlight-location--color-${temporaryHighlightColor ?? mostRecentAnnotation?.color}`,
            returnStringIfTrue(
              hugContainer,
              "highlight-location--hug-container"
            )
          ])
        ),
        returnStringIfTrue(isHovering, "highlight-location--hover"),
        returnStringIfTrue(disabled, "highlight-location--disabled")
      ]}
      width={width}
      contentsWidth={contentsWidth}
      onMouseEnter={() => setIsHovering(true)}
      onMouseLeave={() => setIsHovering(false)}
      ref={parentRef}
      style={{
        ["anchorName" as string]: `--${anchorName}`
      }}
      position="relative"
    >
      {children}
      {!disabled && mode === CollectionFormMode.ANNOTATE && (
        <Box
          className="highlight-location__highlight-box"
          position="absolute"
          width="100"
          height="100"
          onClick={e => {
            if (
              !mostRecentAnnotation ||
              mostRecentAnnotation.color !== currentHighlightColor
            ) {
              setTemporaryHighlightColor(currentHighlightColor);
              setIsOpen(true);
            } else if (!isOpen) {
              setIsOpen(true);
            }

            e.stopPropagation();
            e.preventDefault();
          }}
        />
      )}
      {relevantAnnotations.length > 0 && !isOpen && isHovering && (
        <FloatingHighlightAnnotationLocationIndicator
          parentRef={parentRef}
          anchorName={anchorName}
          relevantAnnotations={relevantAnnotations}
          onClick={e => {
            setIsOpen(true);
            e.stopPropagation();
            e.preventDefault();
          }}
        />
      )}
      {isOpen && (
        <FloatingAnnotations
          parentRef={parentRef}
          anchorName={anchorName}
          onClose={handleClose}
          location={location}
          annotations={relevantAnnotations}
          onOpenInNew={() => {
            summaryModal.setTab(CollectionFormSummaryModalTab.HIGHLIGHTS);
            summaryModal.setLocation(location);
            setIsOpen(false);
            setIsHovering(false);
          }}
          position="top-left"
          onSave={onSaveAnnotation}
          canAddAnnotation={mode === CollectionFormMode.ANNOTATE}
          onlyAddingNewAnnotation={!!temporaryHighlightColor}
          // Name at the top of the floating annotations is always the same
          hideLocationOnEachAnnotation
        />
      )}
    </Box>
  );
};

const FloatingHighlightAnnotationLocationIndicator = ({
  parentRef,
  anchorName,
  relevantAnnotations,
  onClick
}: {
  parentRef: React.RefObject<HTMLDivElement>;
  anchorName: string;
  relevantAnnotations: HighlightAnnotation[];
  onClick?: React.MouseEventHandler<HTMLDivElement>;
}) => {
  return (
    <FloatingWithParent
      parentRef={parentRef}
      anchorName={anchorName}
      position="top-left"
      zIndex={19}
      skipGap
      className="floating-highlight-annotation-location-indicator"
    >
      <Box
        className="floating-highlight-annotation-location-indicator__content"
        alignment="center"
        onClick={onClick}
      >
        <Badge width="fit" count={relevantAnnotations.length} variant="default">
          <Inline
            className="floating-highlight-annotation-location-indicator__icon"
            alignment="center"
          >
            <Icon name="ink_highlighter" size="s" fillStyle="filled" />
          </Inline>
        </Badge>
      </Box>
    </FloatingWithParent>
  );
};
