import React, { useCallback } from "react";

import {
  generatePath,
  useNavigate,
  useOutletContext,
  useParams
} from "react-router-dom";

import { customNanoId } from "@helpers/customNanoIdHelper.ts";

import { DocChange } from "@pages/workspace/WorkspaceLayout.tsx";

import { routeConstants } from "@src/constants/routeConstants";
import { useDictionary } from "@src/hooks/useDictionary.tsx";
import { ConfigurationFormType } from "@src/types/FormConfiguration.ts";

import { ConfigurationFormModal } from "../ConfigurationFormModal";
import "../ConfigurationFormModal.scss";

interface CreateConfigurationFormModalProps {
  isOpen?: boolean;
  onOpenChange: () => void;
}

const emptyFormConfig: Partial<ConfigurationFormType> = {
  name: "",
  key: "",
  description: "",
  seriesId: "",
  level: 0,
  foundationId: "",
  content: []
};

export const CreateConfigurationFormModal = ({
  isOpen = true,
  onOpenChange
}: CreateConfigurationFormModalProps) => {
  const { docChange } = useOutletContext<{
    docChange: DocChange;
  }>();

  const navigate = useNavigate();
  const d = useDictionary();
  const params = useParams();

  const saveFormConfiguration = useCallback(
    ({
      name,
      key,
      description,
      foundationId,
      seriesId,
      allowMultiple,
      labels = []
    }: ConfigurationFormType) => {
      const id = customNanoId();
      docChange(d => {
        d.forms ??= {};
        d.forms[id] = {
          name,
          key,
          level: 0,
          id,
          description,
          metadata: {
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          },
          labels,
          foundationId: foundationId ?? "",
          seriesId: seriesId ?? "",
          allowMultiple: allowMultiple ?? false,
          content: []
        };
      });
      onOpenChange();
      navigate(
        generatePath(routeConstants.configurationForm, {
          ...params,
          configurationFormKey: key
        })
      );
    },
    [docChange, navigate, onOpenChange, params]
  );

  if (!isOpen) {
    return;
  }

  return (
    <ConfigurationFormModal
      isOpen
      handleClose={onOpenChange}
      heading={d("ui.configuration.forms.createModal.title")}
      handleSubmit={saveFormConfiguration}
      defaultValues={emptyFormConfig}
    />
  );
};
