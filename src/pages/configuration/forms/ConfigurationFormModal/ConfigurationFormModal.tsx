import React, { useCallback, useMemo, useState } from "react";

import { Doc } from "@automerge/automerge-repo";
import {
  Divider,
  Form,
  Modal,
  ModalDialog,
  Overlay,
  SelectOptionType,
  Stack,
  getClassNames
} from "@oneteam/onetheme";
import { useOutletContext } from "react-router-dom";
import z from "zod";

import { mapOverResource } from "@helpers/OrderedMapNoState.ts";

import { ConfigurationLabelsSelect } from "@components/shared/ConfigurationLabelsSelect/ConfigurationLabelsSelect";
import { NameKeyFormField } from "@components/shared/NameKeyFormField/NameKeyFormField";
import { autoGenerateKeyFromName } from "@components/shared/NameKeyFormField/autoGenerateKeyFromName";

import { Dictionary, useDictionary } from "@src/hooks/useDictionary.tsx";
import {
  ConfigurationFormType,
  formConfigSchema
} from "@src/types/FormConfiguration";
import { keySchema } from "@src/types/Key";
import { LabelAvailableTo } from "@src/types/Label";
import { WorkspaceDocument } from "@src/types/documentTypes.ts";

import "./ConfigurationFormModal.scss";

interface ConfigurationFormModalProps {
  isOpen: boolean;
  handleClose: () => void;
  defaultValues?: Partial<ConfigurationFormType>;
  disabled?: boolean;
  heading?: string;
  updatingFormId?: string;
  handleSubmit: (formData: ConfigurationFormType) => void;
}

export const ConfigurationFormModal = ({
  isOpen,
  handleClose,
  defaultValues,
  disabled,
  heading,
  updatingFormId,
  handleSubmit
}: ConfigurationFormModalProps) => {
  const d = useDictionary();
  const { document: workspaceConfiguration } = useOutletContext<{
    document: Doc<WorkspaceDocument>;
  }>();
  const [keyError, setKeyError] = useState<string | undefined>(undefined);

  const foundationOptions = useMemo(
    () =>
      mapOverResource(workspaceConfiguration.foundations, foundationConfig => {
        return {
          label: foundationConfig.name,
          value: foundationConfig.id
        };
      }),
    [workspaceConfiguration.foundations]
  );

  const series: SelectOptionType[] = useMemo(
    () =>
      Object.entries(workspaceConfiguration.series ?? {}).map(
        ([seriesId, seriesConfig]) => {
          return { label: seriesConfig.name, value: seriesId };
        }
      ),
    [workspaceConfiguration.series]
  );

  const existingFormKeys = useMemo(() => {
    let forms = Object.values(workspaceConfiguration.forms ?? {});
    if (updatingFormId) {
      // If we're updating a form, we should exclude it from the list of existing keys
      forms = forms.filter(form => form.id !== updatingFormId);
    }
    return forms.map(form => form.key);
  }, [workspaceConfiguration.forms, updatingFormId]);

  const handleSubmitConfigurationForm = useCallback(
    (data: ConfigurationFormType) => {
      let key = data.key?.trim();

      if (!key && !updatingFormId) {
        // If the key is not set, we can set it to the uppercase version of the name
        key =
          autoGenerateKeyFromName(data.name, {
            existingKeys: existingFormKeys
          }) ?? "";
      }

      const keyParsed = (() => {
        try {
          return keySchema(d).parse(key);
        } catch (error) {
          setKeyError((error as z.ZodError).errors[0].message);
        }
      })();

      if (!keyParsed) {
        return;
      }

      handleSubmit({
        ...data,
        key: keyParsed
      });
      handleClose();
    },
    [d, existingFormKeys, handleClose, handleSubmit, updatingFormId]
  );

  if (!isOpen) {
    return null;
  }
  return (
    <ModalDialog isOpen onOpenChange={handleClose}>
      <Overlay isOpen />
      <Modal
        className={getClassNames(["configuration-form-modal"])}
        heading={heading}
        isOpen={isOpen}
        onOpenChange={handleClose}
      >
        <Form
          schema={formConfigSchema(d, existingFormKeys)}
          handleSubmit={handleSubmitConfigurationForm}
          submitLabel={d("ui.common.save")}
          handleCancel={handleClose}
          cancelLabel={d("ui.common.cancel")}
          d={d}
          defaultValues={defaultValues}
          disabled={disabled}
        >
          <ConfigurationFormModalFields
            disabled={disabled}
            updatingFormId={updatingFormId}
            foundationOptions={foundationOptions}
            series={series}
            d={d}
            keyError={keyError}
            existingFormKeys={existingFormKeys}
          />
        </Form>
      </Modal>
    </ModalDialog>
  );
};

const ConfigurationFormModalFields = ({
  disabled = false,
  updatingFormId,
  foundationOptions = [],
  series = [],
  d,
  keyError,
  existingFormKeys = []
}: {
  disabled?: boolean;
  updatingFormId?: string;
  foundationOptions: SelectOptionType[];
  series: SelectOptionType[];
  d: Dictionary;
  keyError?: string;
  existingFormKeys: string[];
}) => {
  return (
    <Stack
      className="configuration-form-modal-fields"
      gap="150"
      height="100"
      overflow="auto"
      style={{
        padding: "var(--spacing-100) 0"
      }}
    >
      <Form.Select
        name="foundationId"
        label={d("ui.terminology.foundationConfiguration")}
        options={foundationOptions}
        disabled={disabled || !!updatingFormId}
        description={d("ui.configuration.forms.fields.foundation.description")}
        required
      />
      <NameKeyFormField
        nameField={{ label: d("ui.common.name") }}
        keyField={{
          label: d("ui.configuration.forms.fields.key.label"),
          tooltip: d("ui.configuration.forms.fields.key.tooltip"),
          error: keyError,
          disabled: disabled || !!updatingFormId,
          existingKeys: existingFormKeys
        }}
      />
      <Form.Select
        name="seriesId"
        label={d("ui.terminology.series")}
        options={series}
        disabled={disabled || !!updatingFormId}
      />
      <Form.Toggle
        label={d("ui.configuration.forms.fields.allowMultiple.label")}
        name="allowMultiple"
        description={d(
          "ui.configuration.forms.fields.allowMultiple.description"
        )}
        disabled={disabled || !!updatingFormId}
      />
      <Divider />
      <Form.TextAreaField
        name="description"
        label={d("ui.configuration.forms.fields.description")}
        withDebounce
        width="100"
        disabled={disabled}
      />
      <ConfigurationLabelsSelect
        isInForm
        label={d("ui.configuration.labels.title")}
        name="labels"
        labelFor={LabelAvailableTo.FORM_CONFIGURATION}
        disabled={disabled}
      />
    </Stack>
  );
};

ConfigurationFormModal.displayName = "ConfigurationFormModal";
