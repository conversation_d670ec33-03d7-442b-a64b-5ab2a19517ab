import React, {
  InputHTMLAttributes,
  SyntheticEvent,
  useCallback,
  useEffect,
  useMemo
} from "react";

import useOnClickOutside from "use-onclickoutside";

import { Box } from "../../fermions/index.ts";
import {
  CommonInputProps,
  returnStringIfTrue
} from "../../helpers/componentHelpers.ts";
import { Alert } from "../Alert/Alert.tsx";
import {
  AlertBackground,
  AlertIcon,
  AlertVariant
} from "../Alert/AlertTypes.ts";
import { Label } from "../Label/Label.tsx";
import "./Renamable.scss";

export interface RenamableProps extends CommonInputProps {
  label?: undefined;
  hidden?: undefined;
  // Controlled
  value?: string;
  onChange?: (value: string) => void;

  // Uncontrolled
  defaultValue?: string;

  minLength?: InputHTMLAttributes<HTMLInputElement>["minLength"];
  maxLength?: InputHTMLAttributes<HTMLInputElement>["maxLength"];

  regex?: RegExp;

  autoComplete?: InputHTMLAttributes<HTMLInputElement>["autoComplete"];

  controlFocus?: boolean;
}

export const Renamable = ({
  controlFocus = undefined,

  name,
  id,
  value,
  onChange,
  defaultValue,
  placeholder,
  description,
  required,
  error,
  disabled,
  minLength,
  maxLength,
  regex,
  autoComplete = "off",
  className = ""
}: React.PropsWithChildren<RenamableProps>) => {
  const textAreaRef = React.useRef<HTMLTextAreaElement>(null);
  const [isRenaming, setIsRenaming] = React.useState(false);
  const [temporaryValue, setTemporaryValue] = React.useState(value);

  const uniqueAnchorName = useMemo(
    () =>
      `--renamable-${
        Math.random().toString(36).substring(2, 15) +
        Math.random().toString(36).substring(2, 15)
      }`,
    []
  );

  const handleStartRenaming = useCallback(
    (e?: SyntheticEvent) => {
      if (disabled) {
        return;
      }
      e?.preventDefault();
      e?.stopPropagation();

      setIsRenaming(current => {
        if (!current) {
          setTemporaryValue(value);
          setTimeout(function () {
            textAreaRef.current?.select();
          }, 25);
        }

        return true;
      });
    },
    [value, disabled]
  );

  const handleStopRenaming = useCallback(() => {
    if (isRenaming) {
      textAreaRef.current?.blur();
      if (
        temporaryValue &&
        temporaryValue?.length > 0 &&
        temporaryValue !== value
      ) {
        onChange?.(temporaryValue);
      } else {
        setTemporaryValue(value);
      }
      setIsRenaming(false);
    }
  }, [isRenaming, onChange, value, temporaryValue]);

  useEffect(() => {
    if (
      controlFocus === true &&
      document.activeElement !== textAreaRef.current
    ) {
      handleStartRenaming();
    } else if (
      controlFocus === false &&
      document.activeElement === textAreaRef.current
    ) {
      handleStopRenaming();
    }
    // Ignore dependency on handleStartRenaming and handleStopRenaming so that we can control the focus without causing an infinite loop
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [controlFocus]);

  const handleChangeInternally = useCallback(
    (updatedValue: string) => {
      if (updatedValue && regex && !regex?.test(updatedValue)) {
        return;
      }
      setTemporaryValue(updatedValue);
    },
    [regex]
  );

  useOnClickOutside(textAreaRef, () => {
    handleStopRenaming();
  });

  return (
    <Box
      position="relative"
      classNames={[
        "renamable",
        returnStringIfTrue(disabled, "renamable--disabled"),
        className
      ]}
      onClick={handleStartRenaming}
      onDrag={!isRenaming ? handleStartRenaming : undefined}
      width="fit"
      alignment="left"
      style={{
        ["anchorName" as string]: uniqueAnchorName
      }}
    >
      {isRenaming ? (
        <Label
          htmlFor={id}
          // Renamable does not have a label
          label=""
          required={required}
          hideRequiredIndicator
          disabled={disabled}
          description={description}
          error={error}
          containerClassName="renamable__label"
        >
          <textarea
            rows={1}
            className="renamable__input"
            ref={textAreaRef}
            autoFocus
            value={temporaryValue}
            onChange={e => handleChangeInternally(e.target.value)}
            onKeyDown={e => {
              if (e.key === "Enter") {
                e.currentTarget.blur();
              }
            }}
            onBlur={handleStopRenaming}
            placeholder={placeholder}
            name={name}
            id={id}
            defaultValue={defaultValue}
            disabled={disabled}
            minLength={minLength}
            maxLength={maxLength}
            autoComplete={autoComplete}
            data-1p-ignore={autoComplete === "off"}
          />
        </Label>
      ) : (
        <>
          <p className="renamable__value">{value}</p>
          {error && (
            <Alert
              className="renamable__error"
              variant={AlertVariant.DANGER}
              background={AlertBackground.TRANSPARENT}
              icon={AlertIcon.NONE}
            >
              {error}
            </Alert>
          )}
        </>
      )}
    </Box>
  );
};
