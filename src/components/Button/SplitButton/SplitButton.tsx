import React, { <PERSON>psWithChildren } from "react";

import { Box, Inline } from "../../../fermions/index.ts";
import { composeComponentClassNames } from "../../../helpers/componentHelpers.ts";
import { ColorOption } from "../../../tokens/IonsInterface.ts";
import { DropdownMenu } from "../../DropdownMenu/DropdownMenu.tsx";
import { FloatingWithParentPosition } from "../../FloatingWithParent/FloatingWithParentTypes.ts";
import { IconSize, IconType } from "../../Icon/IconTypes.ts";
import { Button } from "../Button.tsx";
import { ButtonVariant } from "../ButtonTypes.ts";
import { IconButton } from "../IconButton/IconButton.tsx";
import "./SplitButton.scss";
import { SplitButtonSize, SplitButtonVariant } from "./SplitButtonTypes.ts";

interface SplitButtonProps {
  label: string;
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  onClick?: React.MouseEventHandler<HTMLButtonElement>;
  variant?: `${SplitButtonVariant}`;
  size?: `${SplitButtonSize}`;
  leftIcon?: IconType;
  type?: "submit" | "reset" | "button";
  position?: `${FloatingWithParentPosition}`;
  disabled?: boolean;
  className?: string;
}

export const SplitButton = ({
  label,
  isOpen,
  onOpenChange,
  onClick,
  variant = SplitButtonVariant.PRIMARY,
  size = "default",
  leftIcon,
  type = "button",
  disabled = false,
  className = "",
  position = "bottom-right",
  children
}: PropsWithChildren<SplitButtonProps>) => {
  return (
    <Inline
      className={composeComponentClassNames(
        "split-button",
        {
          size,
          variant
        },
        [className]
      )}
      width="fit"
    >
      <Button
        className="split-button__left"
        leftIcon={leftIcon}
        variant={variant as unknown as ButtonVariant}
        label={label}
        onClick={onClick}
        type={type}
        disabled={disabled}
        size={size}
      />
      <DropdownMenu
        minWidthAsParent
        className="split-button__right"
        isOpen={isOpen}
        onOpenChange={onOpenChange}
        position={position}
        trigger={({ onClick }) => (
          <Box
            className="split-button__right__trigger"
            alignment="center"
            onClick={onClick}
          >
            <IconButton
              name={isOpen ? "keyboard_arrow_up" : "keyboard_arrow_down"}
              size={
                size === SplitButtonSize.SMALL
                  ? IconSize.SMALL
                  : IconSize.REGULAR
              }
              color={
                variant === SplitButtonVariant.PRIMARY
                  ? ColorOption.ON_PRIMARY
                  : ColorOption.ON_SECONDARY
              }
              disabled={disabled}
            />
          </Box>
        )}
      >
        {children}
      </DropdownMenu>
    </Inline>
  );
};
