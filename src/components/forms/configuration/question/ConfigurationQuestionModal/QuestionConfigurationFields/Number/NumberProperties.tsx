import React, { useCallback, useMemo } from "react";

import { Doc, Prop } from "@automerge/automerge-repo";
import {
  Inline,
  NumberField,
  NumberFieldProps,
  NumberType,
  Select,
  SelectValue
} from "@oneteam/onetheme";
import { useOutletContext } from "react-router-dom";

import { getByPath } from "@helpers/configurationFormHelper.ts";
import {
  formatDecimalPlaces,
  formatPositiveInteger
} from "@helpers/numericalHelper";

import { numberOnChange } from "@components/forms/configuration/question/ConfigurationQuestionModal/QuestionConfigurationFields/Number/NumberHelper.ts";

import { DocChange } from "@pages/workspace/WorkspaceLayout.tsx";

import { EXTRA_DECIMAL_PLACES_FOR_PERCENTAGE } from "@src/constants/numberConstants.ts";
import { useConfigurationFormError } from "@src/hooks/formConfiguration/useConfigurationFormError";
import { useDictionary } from "@src/hooks/useDictionary.tsx";
import { Question } from "@src/types/Question.ts";
import { NumberQuestionProperties } from "@src/types/QuestionProperties.ts";
import { WorkspaceDocument } from "@src/types/documentTypes.ts";

export const NumberProperties = ({
  question,
  path,
  disabled
}: {
  question: Question<NumberQuestionProperties>;
  path: Prop[];
  disabled?: boolean;
}) => {
  const d = useDictionary();
  const { docChange } = useOutletContext<{
    document: Doc<WorkspaceDocument>;
    docChange: DocChange;
  }>();

  const { min, max, decimalPlaces, type } = question.properties ?? {};

  const questionAccessor = useCallback(
    (accessor: string) => {
      return `${path.join(".")}.${accessor}`;
    },
    [path]
  );

  const numberTypes = useMemo(() => {
    return Object.values(NumberType).map(value => ({
      value: value,
      label: d(`ui.configuration.forms.question.number.${value}`)
    }));
  }, [d]);

  const extraDecimalPlacesIfPercentage = useMemo(() => {
    return type === NumberType.PERCENTAGE
      ? EXTRA_DECIMAL_PLACES_FOR_PERCENTAGE
      : 0;
  }, [type]);
  const decimalPlacesForUI = useMemo(() => {
    return decimalPlaces
      ? decimalPlaces - extraDecimalPlacesIfPercentage
      : undefined;
  }, [decimalPlaces, extraDecimalPlacesIfPercentage]);

  const onChange = useCallback(
    (field: keyof NumberQuestionProperties) =>
      numberOnChange({ docChange, path, field }),
    [docChange, path]
  );

  const { getServerError } = useConfigurationFormError(path);

  const error = useMemo(() => {
    const serverError = getServerError("min");
    if (serverError?.message) {
      return serverError.message;
    }
    if (
      min !== undefined &&
      max !== undefined &&
      min !== null &&
      max !== null &&
      min > max
    ) {
      return d("errors.configurationForm.question.min.minMax");
    }
    return undefined;
  }, [min, max, d, getServerError]);

  const onChangeDecimalPlaces = useCallback(
    (value: string) => {
      if (value === undefined || value === "") {
        onChange("decimalPlaces")(value);
        return;
      }
      const updatedValue = Number(value) + extraDecimalPlacesIfPercentage;
      onChange("decimalPlaces")(updatedValue.toString());

      // update format for min,max,defaultValue and preview
      docChange(d => {
        const q = getByPath<Question<NumberQuestionProperties>>(d, path);
        if (!q || !q.properties) {
          console.error("Question not found", path);
          return;
        }
        const format = formatDecimalPlaces(updatedValue);
        type KeysWithValsOfType<T, V> = keyof {
          [P in keyof T as T[P] extends V ? P : never]: P;
        } &
          keyof T;
        type NumberKeys = KeysWithValsOfType<NumberQuestionProperties, number>;

        for (const prop of ["min", "max", "defaultValues"] as NumberKeys[]) {
          if (q.properties?.[prop]) {
            (q.properties[prop] as number | "") = Number(
              format(q.properties[prop])
            );
          }
        }
      });
    },
    [docChange, extraDecimalPlacesIfPercentage, onChange, path]
  );

  const format = useMemo(
    () => formatDecimalPlaces(decimalPlaces),
    [decimalPlaces]
  );

  return (
    <>
      <Inline gap="150">
        <Select
          width="100"
          label={d("ui.configuration.forms.question.number.type")}
          name={questionAccessor("properties.type")}
          value={(type ?? NumberType.NUMBER).toString()}
          disabled={disabled}
          onChange={(value: SelectValue | undefined) => {
            docChange(d => {
              const q = getByPath<Question<NumberQuestionProperties>>(d, path);
              if (!q || !q.properties) {
                console.error("Question not found", path);
                return;
              }
              if (value === undefined) {
                delete q.properties.type;
              } else {
                const previousType = q.properties.type;
                if (
                  value === NumberType.PERCENTAGE &&
                  previousType !== NumberType.PERCENTAGE
                ) {
                  if (q.properties.decimalPlaces !== undefined) {
                    q.properties.decimalPlaces +=
                      EXTRA_DECIMAL_PLACES_FOR_PERCENTAGE;
                  }
                } else if (
                  value !== NumberType.PERCENTAGE &&
                  previousType === NumberType.PERCENTAGE
                ) {
                  if (q.properties.decimalPlaces !== undefined) {
                    q.properties.decimalPlaces -=
                      EXTRA_DECIMAL_PLACES_FOR_PERCENTAGE;
                  }
                }
                q.properties.type = value as NumberFieldProps["type"];
              }
            });
          }}
          options={numberTypes}
          onlyTriggerChangeWhenBlur
        />
        <NumberField
          width="100"
          label={d("ui.configuration.forms.question.number.decimalPlaces")}
          name={questionAccessor("properties.decimalPlaces")}
          value={decimalPlacesForUI ? String(decimalPlacesForUI) : ""}
          disabled={disabled}
          min={0} // cannot have decimal places less than 0
          format={formatPositiveInteger}
          allowClear={true}
          onChange={onChangeDecimalPlaces}
          onlyTriggerChangeWhenBlur
        />
      </Inline>
      <Inline gap="150">
        <NumberField
          width="100"
          label={d("ui.configuration.forms.question.number.min")}
          name={questionAccessor("properties.min")}
          value={min !== null && min !== undefined ? String(min) : ""}
          type={type}
          disabled={disabled}
          allowClear={true}
          onChange={onChange("min")}
          format={format}
          error={error}
          step={decimalPlacesForUI ? Math.pow(10, -decimalPlacesForUI) : 1}
          allowedDecimalPlaces={decimalPlaces}
          extraDecimalPlacesForPercentage={EXTRA_DECIMAL_PLACES_FOR_PERCENTAGE}
          onlyTriggerChangeWhenBlur
        />
        <NumberField
          width="100"
          label={d("ui.configuration.forms.question.number.max")}
          name={questionAccessor("properties.max")}
          value={max !== null && max !== undefined ? String(max) : ""}
          type={type}
          disabled={disabled}
          allowClear={true}
          onChange={onChange("max")}
          format={format}
          step={decimalPlacesForUI ? Math.pow(10, -decimalPlacesForUI) : 1}
          allowedDecimalPlaces={decimalPlaces}
          extraDecimalPlacesForPercentage={EXTRA_DECIMAL_PLACES_FOR_PERCENTAGE}
          onlyTriggerChangeWhenBlur
        />
      </Inline>
    </>
  );
};
