// .table-question {
//   position: relative;
//   max-height: 440px;

//   &__container {
//     padding-bottom: var(--spacing-100);
//     gap: var(--spacing-050);
//   }
//   &__table {
//     width: 100%;
//     border-collapse: collapse;
//     min-width: fit-content;
//     border: 1px solid var(--color-border);
//     background-color: var(--color-surface-secondary);

//     &__header {
//       position: sticky;
//       top: -2px;
//       z-index: 11;
//       &__row {
//         border-bottom: 1px solid var(--color-border);
//       }

//       &__cell {
//         padding: var(--components-table-cell-padding-vertical, 6px)
//           var(--components-table-cell-padding-horizontal, 6px);
//         background-color: var(--color-surface-primary);
//         min-width: fit-content;
//         border-right: 1px solid var(--color-border);
//         position: sticky;
//         z-index: 1;
//         &:last-child {
//           border-right: none;
//         }
//         &__label {
//           width: max-content;
//           & .label__text,
//           & .label__required {
//             font-size: var(--components-table-header-font-size, 12px);
//           }
//         }

//         &:has(.floating-annotations) {
//           z-index: 20;
//         }
//         &.table-question__table__data__counter:has(.floating-annotations) {
//           z-index: 20;
//         }
//       }
//     }

//     &__data {
//       &__counter {
//         @extend .table-question__table__header__cell;
//         width: var(--spacing-300);
//         min-width: var(--spacing-300, 24px);
//         position: sticky;
//         left: -2px;
//         z-index: 10;
//         background-color: var(--color-surface-primary);

//         & .label__container {
//           align-items: center;
//           justify-self: center;
//         }
//       }

//       &__row {
//         border-bottom: 1px solid var(--color-border);
//         &:last-child {
//           border-right: none;
//         }

//         &--selected {
//           background-color: var(--color-colorLightest);
//           & .table-question__table__data__counter {
//             background-color: var(--color-colorLightest);
//           }
//         }

//         .delete-row-icon {
//           visibility: hidden;
//           opacity: 0;
//           background-color: var(--color-surface-secondary);
//           &--with-annotations,
//           &--open {
//             opacity: 1;
//             visibility: visible;
//           }
//         }

//         &:hover,
//         &:focus-within {
//           & .delete-row-icon {
//             opacity: 1;
//             visibility: visible;
//           }
//         }
//         & .table-question__table__data__counter {
//           & .collection-question-block-comment {
//             visibility: hidden;
//             opacity: 0;
//             &--with-annotations {
//               opacity: 1;
//               visibility: visible;
//             }
//           }
//           &:hover {
//             .collection-question-block-comment {
//               visibility: visible;
//               opacity: 1;
//             }
//           }
//           &:has(.collection-question-block-comment--open) {
//             .collection-question-block-comment {
//               visibility: visible;
//               opacity: 1;
//             }
//           }
//         }
//       }

//       &__cell {
//         position: relative;
//         outline-offset: -2px;
//         border-right: 1px solid var(--color-border);
//         padding: 2px;

//         &--selected {
//           background-color: var(--color-colorLightest);
//           & .table-question__table__data__counter {
//             background-color: var(--color-colorLightest);
//           }
//         }

//         &--actions {
//           position: sticky !important;
//           right: -1px;
//           z-index: 2;
//           border-right: none;
//           width: var(--spacing-300);
//           &:has(.collection-question-block-comment--open) {
//             z-index: 30;
//           }
//         }

//         & .question-answer {
//           &:has(input:disabled):not(
//               :has(.select-properties-options__item),
//               :has(.floating-annotations)
//             ),
//           &:has(.delete-row-icon:disabled) {
//             background-color: var(
//               --components-inputs-disabled-color-background,
//               #ecf2f6
//             );
//           }

//           &__field {
//             &:not(
//               :has(.select-properties-options__item),
//               :has(.floating-annotations)
//             ) {
//               & .text-field__value,
//               & .number-field__value,
//               & .select,
//               & .text-area-field__value,
//               & .date-picker__value {
//                 border-radius: 0;
//                 border: none;
//                 outline: none !important;
//                 background-color: transparent;

//                 & textarea {
//                   width: fit-content;
//                   align-self: flex-start;
//                 }

//                 & input {
//                   width: 100%;
//                   min-width: var(--spacing-1000);
//                 }
//               }

//               & .number-field {
//                 width: 100%;
//               }
//             }

//             & .highlight-location--highlighted {
//               & .text-field__value,
//               & .number-field__value,
//               & .select,
//               & .text-area-field__value,
//               & .date-picker__value {
//                 background-color: transparent !important;
//               }
//             }

//             & .radio-group {
//               padding: var(--components-table-cell-padding-horizontal, 6px);
//             }
//           }

//           &:not(
//             :has(.select-properties-options__item),
//             :has(.floating-annotations)
//           ) {
//             &:has(.question-answer__field:focus-within) {
//               outline: 2px solid
//                 var(--components-inputs-focus-color-border, #0083ff);
//             }
//           }
//         }
//       }
//     }
//   }
// }

.question-answer {
  color: red;
  &:has(input:disabled):not(
      :has(.select-properties-options__item),
      :has(.floating-annotations)
    ),
  &:has(.delete-row-icon:disabled) {
    background-color: var(
      --components-inputs-disabled-color-background,
      #ecf2f6
    );
  }

  &:not(:has(.select-properties-options__item), :has(.floating-annotations)) {
    & .text-field__value,
    & .number-field__value,
    & .select,
    & .text-area-field__value,
    & .date-picker__value {
      border-radius: 0;
      border: none;
      outline: none !important;
      background-color: transparent;

      & textarea {
        width: fit-content;
        align-self: flex-start;
      }

      & input {
        width: 100%;
        min-width: var(--spacing-1000);
      }
    }

    & .number-field {
      width: 100%;
    }

    & .highlight-location--highlighted {
      & .text-field__value,
      & .number-field__value,
      & .select,
      & .text-area-field__value,
      & .date-picker__value {
        background-color: transparent !important;
      }
    }

    & .radio-group {
      padding: var(--components-table-cell-padding-horizontal, 6px);
    }
  }

  &:not(:has(.select-properties-options__item), :has(.floating-annotations)) {
    &:has(.question-answer__field:focus-within) {
      outline: 2px solid var(--components-inputs-focus-color-border, #0083ff);
    }
  }
}
