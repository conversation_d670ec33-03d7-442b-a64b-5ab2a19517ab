import { TableFieldCellValue } from "@oneteam/onetheme";

import { customNanoId } from "@helpers/customNanoIdHelper";

import { Question, QuestionTypes } from "@src/types/Question";
import { ListQuestionProperties } from "@src/types/QuestionProperties";
import {
  FormAnswer,
  ListAnswer,
  ListAnswerItem,
  TableAnswerRow
} from "@src/types/collection/CollectionForm";

export const mapOverCellValues = (
  cells: Record<string, TableFieldCellValue>,
  f: (columnId: string, cellValue: TableFieldCellValue) => void
) => {
  Object.entries(cells).forEach(([columnId, cellValue]) =>
    f(columnId, cellValue)
  );
};

export const setRowCellValue = (
  answer: TableAnswerRow,
  columnId: string,
  cellValue: TableFieldCellValue,
  displayColumnQuestions?: Question[]
) => {
  const column = displayColumnQuestions?.find(q => q.id === columnId);
  if (!column) {
    return;
  }

  const existingValue = answer.columns[columnId]?.value;
  const processedCellValue =
    column?.type === QuestionTypes.LIST
      ? setListValue(cellValue, column, existingValue as ListAnswer)
      : cellValue;

  answer.columns[columnId] = setCellData(
    columnId,
    processedCellValue as FormAnswer["value"],
    column
  );
};

export const setCellData = (
  columnId: string,
  value: FormAnswer["value"],
  column: Question
): FormAnswer => {
  return {
    questionId: columnId,
    type: column?.type ?? "text",
    value: value as FormAnswer["value"]
  };
};

export const setListValue = (
  value: TableFieldCellValue,
  column: Question,
  existingList?: ListAnswer
): ListAnswer => {
  if (!Array.isArray(value)) {
    return value as ListAnswer;
  }

  const entities: { [key: string]: ListAnswerItem } = {};
  const order: string[] = [];

  // Get item question from column properties
  const itemQuestion = (column.properties as ListQuestionProperties)?.items[0];

  value.forEach((item, index) => {
    // update existing item ID if available
    const existingId = existingList?.order[index];
    const itemId = existingId || customNanoId();

    entities[itemId] = {
      id: itemId,
      item: {
        [itemQuestion.id]: {
          questionId: itemQuestion.id,
          type: itemQuestion.type as FormAnswer["type"],
          value: item
        }
      }
    };

    order.push(itemId);
  });

  return {
    entities,
    order
  } as ListAnswer;
};
