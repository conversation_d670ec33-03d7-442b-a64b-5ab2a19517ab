import React from "react";

import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { Box } from "../../fermions/index.ts";
import { Chart } from "./Chart.tsx";

const meta: Meta<typeof Chart> = {
  component: Chart,
  title: "components/Chart",
  argTypes: {}
};

export default meta;

type Story = StoryObj<typeof Chart>;

const mockData = [
  {
    entity: "entity_1",
    openingBalance: "5000",
    closingBalance: "1000",
    expenseA: "3000",
    expenseB: "1000"
  },
  {
    entity: "entity_2",
    openingBalance: "10000",
    closingBalance: "7000",
    expenseA: "2000",
    expenseB: "1000"
  },
  {
    entity: "entity_3",
    openingBalance: "8000",
    closingBalance: "1000",
    expenseA: "4000",
    expenseB: "3000"
  }
];

const basicLineChartProp: Story["args"] = {
  option: {
    title: { text: "Basic Line Chart" },
    tooltip: {
      trigger: "axis"
      // axisPointer: {
      //   type: "cross"
      // }
    },
    toolbox: {
      show: true,
      feature: {
        // dataZoom: {
        //   yAxisIndex: "none"
        // },
        dataView: { readOnly: false },
        magicType: { type: ["line", "bar"] },
        restore: {},
        saveAsImage: {}
      }
    },
    grid: {
      left: "10%",
      right: "10%",
      bottom: "15%", // Increased bottom margin for rotated labels
      top: "15%", // Added top margin
      containLabel: true // Important: ensures labels are included in layout
    },
    yAxis: {
      type: "value"
    },
    // xAxis: { type: "category", data: ["A", "B", "C", "D", "E", "F"] },
    // series: [
    //   {
    //     name: "Test 1",
    //     type: "line",
    //     data: [5, 10, 8, 15, 20, 36]
    //     // smooth: true
    //     // areaStyle: {},
    //     // stack: "Total"
    //   }
    // ]
    color: [
      "var(--color-traffic-success)",
      "#9a7fd1",
      "#6d8ddf",
      "#3fa7dc",
      "#37a2da"
    ]
  },
  style: { height: 300, width: 300 }
};

const defaultRender = (args: Story["args"]) => {
  return (
    <Box style={{ width: "400px", height: "400px", margin: "auto" }}>
      <Chart
        option={args?.option}
        style={args?.style}
        config={args?.config}
        values={args?.values}
        margin="050"
      />
    </Box>
  );
};

export const BasicLineChart: Story = {
  args: {
    ...basicLineChartProp,
    config: {
      xAxis: {
        options: {
          type: "category",
          axisLabel: {
            interval: 0
          }
        },
        labels: ["entity"]
      },
      xAxisKey: ["entity"],
      seriesKeys: ["openingBalance"],
      seriesProperties: {
        type: "line"
      },
      swapColumns: false
    },
    values: mockData
  },
  render: defaultRender
};

export const SwapLineColumnsAndRowsChart: Story = {
  args: {
    ...basicLineChartProp,
    config: {
      xAxisKey: ["openingBalance", "closingBalance"],
      seriesKeys: ["entity"],
      seriesProperties: {
        type: "line"
      },
      swapColumns: true
    },
    values: mockData
  },
  render: defaultRender
};

export const SmoothedLineChart: Story = {
  args: {
    ...basicLineChartProp,
    config: {
      xAxisKey: "entity",
      seriesKeys: ["openingBalance", "closingBalance"],
      seriesProperties: {
        type: "line",
        smooth: true
      },
      swapColumns: false
    },
    values: mockData
  },
  render: defaultRender
};

export const StackedLineChart: Story = {
  args: {
    ...basicLineChartProp,
    config: {
      xAxisKey: "entity",
      seriesKeys: ["openingBalance", "closingBalance"],
      seriesProperties: {
        type: "line",
        stack: "Total"
      },
      swapColumns: false
    },
    values: mockData
  },
  render: defaultRender
};

export const StackedAreaChart: Story = {
  args: {
    ...basicLineChartProp,
    config: {
      xAxisKey: "entity",
      seriesKeys: ["openingBalance", "closingBalance"],
      seriesProperties: {
        type: "line",
        stack: "Total",
        areaStyle: {},
        emphasis: {
          focus: "series"
        }
      },
      swapColumns: false
    },
    values: mockData
  },
  render: defaultRender
};

export const BasicColumnChart: Story = {
  args: {
    ...basicLineChartProp,
    config: {
      xAxisKey: "entity",
      seriesKeys: ["openingBalance", "closingBalance"],
      seriesProperties: {
        type: "bar"
      },
      swapColumns: false
    },
    values: mockData
  },
  render: defaultRender
};

export const BasicPieChart: Story = {
  args: {
    ...basicLineChartProp,
    option: {
      // title: {
      //   text: "Basic Pie Chart",
      //   subtext: "Mock Data"
      // },
      tooltip: {
        trigger: "item"
      },
      legend: {
        orient: "vertical",
        right: "right"
      }
    },
    config: {
      label: ["entity"],
      value: "openingBalance",
      seriesProperties: {
        type: "pie",
        radius: "50%",
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: "rgba(0, 0, 0, 0.5)"
          }
        }
      },
      swapColumns: false
    },
    values: mockData
  },
  render: defaultRender
};

export const SwapPieColumnsAndRowsChart: Story = {
  args: {
    ...basicLineChartProp,
    option: {
      // title: {
      //   text: "Basic Pie Chart",
      //   subtext: "Mock Data"
      // },
      tooltip: {
        trigger: "item"
      },
      legend: {
        orient: "horizontal",
        right: "0",
        bottom: "0"
      }
    },
    config: {
      label: ["openingBalance", "closingBalance", "expenseA", "expenseB"],
      value: "entity",
      seriesProperties: {
        type: "pie",
        radius: "50%",
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: "rgba(0, 0, 0, 0.5)"
          }
        }
      },
      swapColumns: true,
      rowIndex: 1
    },
    values: mockData
  },
  render: defaultRender
};
