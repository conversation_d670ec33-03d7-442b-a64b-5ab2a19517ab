import { useMemo } from "react";

interface DataItem {
  [key: string]: string | number;
}

// interface GraphConfig {
//   xAxisKey: string[]; // Changed from string[] to string
//   seriesKeys: string[];
//   label?: string | string[]; // For pie chart label field
//   value?: string; // For pie chart value field
//   seriesProperties?: Record<string, any>; // Additional properties for series
//   swapColumns?: boolean;
//   rowIndex?: number;
// }

export const useGraphData = (data: DataItem[], config: GraphConfig) => {
  return useMemo(() => {
    if (config.seriesProperties?.type === "pie") {
      if (config.swapColumns && config.rowIndex) {
        // Find the selected row
        const selectedRow = data[config.rowIndex - 1];

        if (!selectedRow) return { series: [] };

        // Transform row data into series format
        return {
          series: [
            {
              name: "Values",
              ...config.seriesProperties,
              data: Object.entries(selectedRow)
                .filter(([key]) => {
                  if (Array.isArray(config.label)) {
                    return config.label.includes(key);
                  }
                  return key !== config.xAxisKey[0] && key !== config.value;
                })
                .map(([key, value]) => ({
                  name: key,
                  value: Number(value)
                }))
            }
          ]
        };
      }

      // Original pie chart logic
      return {
        series: [
          {
            name: config.value,
            data: data.map(item => ({
              name: String(item[(config.label as string) || ""]),
              value: Number(item[config.value || ""])
            })),
            ...config.seriesProperties
          }
        ]
      };
    }

    if (!config.swapColumns) {
      return {
        xAxis: {
          type: "category",
          data: data.map(item => item[config.xAxisKey[0]] || "")
        },
        series: config.seriesKeys.map(key => ({
          ...config.seriesProperties,
          name: key,
          data: data.map(item => item[key])
        }))
      };
    } else {
      return {
        xAxis: {
          type: "category",
          data: config.xAxisKey
        },
        series: data.map(item => ({
          ...config.seriesProperties,
          name: item[config.seriesKeys[0]] || "",
          data: config.xAxisKey.map(key => item[key])
        }))
      };
    }
  }, [data, config]);
};
