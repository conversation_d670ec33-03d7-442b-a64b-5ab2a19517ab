import { BoxProps } from "../../fermions";
import { ChartSubType, ChartType } from "./ChartTypes";

// In a chartConfig.ts file
export const getBaseOptions = ({ title }: { title?: string }) => {
  // returns EChartOptions
  // Colors, fonts, animations etc. for onetheme
  return {
    title: { text: "Basic Line Chart" },
    tooltip: {
      trigger: "axis"
      // axisPointer: {
      //   type: "cross"
      // }
    },
    toolbox: {
      show: true,
      feature: {
        // dataZoom: {
        //   yAxisIndex: "none"
        // },
        dataView: { readOnly: false },
        magicType: { type: ["line", "bar"] },
        restore: {},
        saveAsImage: {}
      }
    },
    grid: {
      left: "10%",
      right: "10%",
      bottom: "15%", // Increased bottom margin for rotated labels
      top: "15%", // Added top margin
      containLabel: true // Important: ensures labels are included in layout
    },
    yAxis: {
      type: "value"
    }
  };
};

const getOptionsForType: {
  [key in ChartType]: ({ subType }: { subType: ChartSubType }) => any;
} = {
  // EChartOptions instead of any
  [ChartType.LINE]: ({ subType }) => {
    // specific options for basic line chart
  },
  [ChartType.BAR]: ({ subType }) => {
    // specific options for basic bar chart
  },
  [ChartType.PIE]: ({ subType }) => {
    // specific options for basic pie chart
  }
};

// In Chart.ts
// _.merge(
//   {},
//   getBaseOptions({ title }),
//   getOptionsForType[type]({ subType }),
//   advancedOptions
// );

type LineChartProperties = {
  xAxis: any;
  /// etc
};
