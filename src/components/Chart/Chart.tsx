import React from "react";

import { EChartsOption } from "echarts";
import ReactECharts from "echarts-for-react";
import { extend } from "lodash";

import { Box, BoxProps } from "../../fermions";
import { getBaseOptions } from "./ChartOptions";
import { ChartProps } from "./ChartTypes";
import { useGraphData } from "./useChart";

// interface TooltipOptions {
//   trigger: "axis" | "item";
//   axisPointer?: {
//     type: "cross" | "line" | "shadow";
//   };
// }

// interface LegendOptions {
//   orient?: "vertical" | "horizontal";
//   left?: string;
//   right?: string;
//   bottom?: string;
//   top?: string;
// }

// interface ToolboxFeature {
//   dataZoom?: { yAxisIndex: string };
//   dataView?: { readOnly: boolean };
//   magicType?: { type: string[] };
//   restore?: {};
//   saveAsImage?: {};
// }

// interface ToolboxOptions {
//   show: boolean;
//   feature: ToolboxFeature;
// }

// interface GridOptions {
//   left: string;
//   right: string;
//   bottom: string;
//   top: string;
//   containLabel: boolean;
// }

// interface YAxisOptions {
//   type: "value" | "category";
// }

// interface ChartOptions {
//   title?: {
//     text: string;
//     subtext?: string;
//   };
//   tooltip?: TooltipOptions;
//   legend?: LegendOptions;
//   toolbox?: ToolboxOptions;
//   grid?: GridOptions;
//   yAxis?: YAxisOptions;
//   xAxis?: {
//     type: "category";
//     data: string[];
//   };
//   series?: Array<{
//     name: string;
//     type: "line" | "bar";
//     data: number[];
//     smooth?: boolean;
//     areaStyle?: {};
//     stack?: string;
//   }>;
//   color?: string[];
// }

// export interface GraphProps extends BoxProps {
//   option?: EChartsOption;
//   config: any;
//   values: any;
// }

export const Chart = ({
  title,
  description,
  data,
  config,
  ...rest
}: ChartProps) => {
  const option = getBaseOptions({ title });
  const graphData = useGraphData(data, config);
  let optionWithData;
  if (config.seriesProperties?.type === "pie") {
    optionWithData = {
      ...option,
      series: graphData?.series
    };
  } else {
    optionWithData = {
      ...option,
      xAxis: {
        ...graphData?.xAxis,
        axisLabel: {
          interval: 0 // Show all labels
        }
      },
      // xAxis: graphData?.xAxis,
      series: graphData?.series
    };
  }

  return (
    <Box className="graph" {...rest}>
      <ReactECharts
        option={optionWithData}
        style={{ width: "100%", height: "100%" }}
      />
      ;
    </Box>
  );
};
