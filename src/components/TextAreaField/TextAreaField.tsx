import React, {
  InputHTMLAttributes,
  useCallback,
  useEffect,
  useMemo
} from "react";

import { debounce } from "lodash";

import { Box, Inline } from "../../fermions/index.ts";
import { CommonInputProps } from "../../helpers/componentHelpers.ts";
import { useWarningCheckForInputs } from "../../hooks/useWarningCheckForInputs.tsx";
import { ColorText } from "../../tokens/IonsInterface.ts";
import { CloseIconButton } from "../Button/index.ts";
import { IconSize } from "../Icon/IconTypes.ts";
import { Label } from "../Label/Label.tsx";
import { LabelTextPosition } from "../Label/LabelTypes.ts";
import "./TextAreaField.scss";

export interface TextAreaFieldProps extends CommonInputProps {
  // Controlled
  value?: string;
  onChange?: (value: string) => void;
  valueSequenceId?: number; // use this to force re-render

  // Uncontrolled
  defaultValue?: string;

  minLength?: InputHTMLAttributes<HTMLInputElement>["minLength"];
  maxLength?: InputHTMLAttributes<HTMLInputElement>["maxLength"];

  regex?: RegExp;

  withDebounce?: boolean;
  debounceTime?: number;
  autoComplete?: InputHTMLAttributes<HTMLInputElement>["autoComplete"];

  onlyTriggerChangeWhenBlur?: boolean;

  disableResize?: boolean;
  userControlResize?: boolean;

  enterForNewLine?: boolean;
  allowClear?: boolean;
  controlFocus?: boolean;
}

export const TextAreaField = ({
  name,
  id,
  label = "",
  value,
  onChange,
  defaultValue,
  placeholder,
  description,
  tooltip,
  required,
  hidden,
  error,
  disabled,
  width = "fit",
  minLength,
  maxLength,
  regex,
  autoComplete = "off",
  disableResize = false,
  withDebounce,
  debounceTime = 300,
  onlyTriggerChangeWhenBlur = false,
  onClick,
  onFocus,
  onBlur,
  onKeyDown,
  onKeyUp,
  onPaste,
  className,
  valueSequenceId,
  controlFocus,
  userControlResize = false,
  allowClear = !required,
  enterForNewLine = false
}: TextAreaFieldProps) => {
  useWarningCheckForInputs({ value, defaultValue, onChange });

  const textAreaRef = React.useRef<HTMLTextAreaElement>(null);
  const [tempValue, setTempValue] = React.useState(
    value ? value : defaultValue
  );

  const debounceCalculate = useMemo(
    () => (onChange ? debounce(onChange, debounceTime) : () => {}),
    [debounceTime, onChange]
  );

  const calculateHeight = useCallback(() => {
    const userAgent = navigator.userAgent.toLowerCase();
    const isFieldSizingSupported =
      userAgent.includes("chrome") || userAgent.includes("edge");
    // Chrome and edge support 'field-sizing: content' so we don't need to adjust the height
    if (isFieldSizingSupported || userControlResize || !textAreaRef.current) {
      return;
    }
    textAreaRef.current.style.height = "inherit";
    textAreaRef.current.style.height = `${textAreaRef.current?.scrollHeight}px`;
  }, [textAreaRef, userControlResize]);

  useEffect(() => {
    if (!textAreaRef.current) {
      return;
    }
    calculateHeight();
    if (
      controlFocus === true &&
      document.activeElement !== textAreaRef.current
    ) {
      textAreaRef.current.focus();
      textAreaRef.current.selectionStart = textAreaRef.current?.value.length;
      textAreaRef.current.selectionEnd = textAreaRef.current?.value.length;
    } else if (
      controlFocus === false &&
      document.activeElement === textAreaRef.current
    ) {
      textAreaRef.current.blur();
    }
  }, [calculateHeight, controlFocus]);

  const handleChangeInternally = useCallback(
    (
      updatedValue: string,
      changeWithDebounce = withDebounce,
      notifyExternalChange = !onlyTriggerChangeWhenBlur
    ) => {
      if (updatedValue && regex && !regex?.test(updatedValue)) {
        return;
      }

      setTempValue(updatedValue);
      if (!onChange || !notifyExternalChange) {
        return;
      }

      if (updatedValue === value) {
        return;
      }
      if (changeWithDebounce) {
        debounceCalculate(updatedValue);
      } else {
        onChange(updatedValue);
      }
    },
    [
      value,
      withDebounce,
      onlyTriggerChangeWhenBlur,
      regex,
      onChange,
      debounceCalculate
    ]
  );

  useEffect(() => {
    // The valueSequenceId is used to force re-render the component because there may be cases where in a controlled component
    //  the value get updated to the same value, but differs from tempValue, thus causing a stale value to be displayed
    if (document.activeElement === textAreaRef.current) {
      return;
    }
    handleChangeInternally(value ? value : (defaultValue ?? ""), false, false);
    setTimeout(() => {
      calculateHeight();
    }, 0);
  }, [value, valueSequenceId, defaultValue, calculateHeight]);

  return (
    <Box classNames={["text-area-field", className]} width={width}>
      <Label
        htmlFor={id}
        label={label}
        required={required}
        hidden={hidden}
        disabled={disabled}
        textPosition={LabelTextPosition.TOP}
        description={description}
        tooltip={tooltip}
        error={error}
      >
        <Inline className="text-area-field__value" alignment="left">
          <Box className="text-area-field__left" width="100">
            <textarea
              rows={1}
              ref={textAreaRef}
              className="text-area-field__input"
              placeholder={placeholder}
              onChange={e => {
                handleChangeInternally?.(e.target.value);
                calculateHeight();
              }}
              onBlur={e => {
                onBlur?.(e);
                if (onlyTriggerChangeWhenBlur) {
                  handleChangeInternally?.(e.target.value, false, true);
                }
              }}
              onKeyDown={e => {
                onKeyDown?.(e);
                if (!enterForNewLine && e.key === "Enter" && !e.shiftKey) {
                  e.currentTarget.blur();
                } else {
                  e.stopPropagation();
                }
                if (e.key === "Enter" && e.shiftKey) {
                  const textarea = e.target as HTMLTextAreaElement;
                  const cursorPosition = textarea.selectionStart;
                  const value = textarea.value;
                  const newValue =
                    value.slice(0, cursorPosition) +
                    "\n" +
                    value.slice(cursorPosition);
                  handleChangeInternally(newValue, false, false);
                  requestAnimationFrame(() => {
                    textarea.setSelectionRange(
                      cursorPosition + 1,
                      cursorPosition + 1
                    );
                  });
                  setTimeout(() => {
                    calculateHeight();
                  }, 0);
                  e.preventDefault();
                  e.stopPropagation();
                }
              }}
              onFocus={onFocus}
              onKeyUp={onKeyUp}
              onClick={onClick}
              onPaste={onPaste}
              name={name}
              id={id}
              value={tempValue}
              disabled={disabled}
              style={{
                resize:
                  disableResize || !userControlResize ? "none" : "vertical"
              }}
              minLength={minLength}
              maxLength={maxLength}
              autoComplete={autoComplete}
              data-1p-ignore={autoComplete === "off"}
            />
          </Box>
          <Inline className="text-field__right" gap="000" alignment="right">
            {allowClear &&
              !disabled &&
              (tempValue === undefined ? !!defaultValue : !!tempValue) && (
                <CloseIconButton
                  className="text-area-field__clear"
                  onClick={e => {
                    handleChangeInternally("", false, true);
                    setTimeout(() => {
                      calculateHeight();
                    }, 0);
                    e.stopPropagation();
                    e.preventDefault();
                  }}
                  skipFocus
                  color={ColorText.PRIMARY}
                  size={IconSize.SMALL}
                  disabled={disabled}
                />
              )}
          </Inline>
        </Inline>
      </Label>
    </Box>
  );
};
