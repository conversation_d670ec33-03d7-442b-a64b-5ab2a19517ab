import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState
} from "react";

import { nanoid } from "nanoid";

import { Box } from "../../fermions/index.ts";
import {
  composeComponentClassNames,
  returnStringIfTrue
} from "../../helpers/componentHelpers.ts";
import { useWindowSize } from "../../hooks/useWindowSize.tsx";
import "./FloatingWithParent.scss";
import { FloatingWithParentPosition } from "./FloatingWithParentTypes.ts";

interface FloatingWithParentProps {
  parentRef: React.RefObject<HTMLElement>;
  position?: `${FloatingWithParentPosition}`;
  minWidthAsParent?: boolean;

  // TODO: remove from storybook and then here
  closeWhenParentOutOfView?: boolean;
  allowOutsideWindow?: boolean;
  onOpenChange?: (isOpen: boolean) => void;
  className?: string;
  anchorName?: string;
  skipGap?: boolean;
  zIndex?: number;
}

const positionSwapPriorities: {
  [key in FloatingWithParentPosition]: `${FloatingWithParentPosition}`[];
} = {
  [FloatingWithParentPosition.RIGHT]: [
    FloatingWithParentPosition.LEFT,
    FloatingWithParentPosition.BOTTOM_LEFT,
    FloatingWithParentPosition.BOTTOM_RIGHT
  ],
  [FloatingWithParentPosition.LEFT]: [
    FloatingWithParentPosition.RIGHT,
    FloatingWithParentPosition.BOTTOM_RIGHT,
    FloatingWithParentPosition.BOTTOM_LEFT
  ],
  [FloatingWithParentPosition.BOTTOM_RIGHT]: [
    FloatingWithParentPosition.BOTTOM_LEFT,
    FloatingWithParentPosition.TOP_RIGHT,
    FloatingWithParentPosition.TOP_LEFT
  ],
  [FloatingWithParentPosition.BOTTOM_LEFT]: [
    FloatingWithParentPosition.BOTTOM_RIGHT,
    FloatingWithParentPosition.TOP_LEFT,
    FloatingWithParentPosition.TOP_RIGHT
  ],
  [FloatingWithParentPosition.TOP_LEFT]: [
    FloatingWithParentPosition.TOP_RIGHT,
    FloatingWithParentPosition.BOTTOM_LEFT,
    FloatingWithParentPosition.BOTTOM_RIGHT
  ],
  [FloatingWithParentPosition.TOP_RIGHT]: [
    FloatingWithParentPosition.TOP_LEFT,
    FloatingWithParentPosition.BOTTOM_RIGHT,
    FloatingWithParentPosition.BOTTOM_LEFT
  ],
  [FloatingWithParentPosition.LEFT_CENTER]: [
    FloatingWithParentPosition.RIGHT_CENTER,
    FloatingWithParentPosition.BOTTOM_LEFT,
    FloatingWithParentPosition.BOTTOM_RIGHT
  ],
  [FloatingWithParentPosition.RIGHT_CENTER]: [
    FloatingWithParentPosition.LEFT_CENTER,
    FloatingWithParentPosition.BOTTOM_RIGHT,
    FloatingWithParentPosition.BOTTOM_LEFT
  ],
  [FloatingWithParentPosition.LEFT_BOTTOM]: [
    FloatingWithParentPosition.RIGHT_BOTTOM,
    FloatingWithParentPosition.BOTTOM_LEFT,
    FloatingWithParentPosition.BOTTOM_RIGHT
  ],
  [FloatingWithParentPosition.RIGHT_BOTTOM]: [
    FloatingWithParentPosition.LEFT_BOTTOM,
    FloatingWithParentPosition.BOTTOM_RIGHT,
    FloatingWithParentPosition.BOTTOM_LEFT
  ]
};

export const FloatingWithParent = ({
  parentRef,
  position: idealPosition = FloatingWithParentPosition.BOTTOM_LEFT,
  minWidthAsParent = false,
  className = "",
  anchorName,
  zIndex = 10,
  allowOutsideWindow = false,
  skipGap = false,
  children
}: React.PropsWithChildren<FloatingWithParentProps>) => {
  const { width: windowWidth, height: windowHeight } = useWindowSize();
  const floatingWithParentRef = useRef<HTMLDivElement>(null);
  const [initialCalculationComplete, setInitialCalculationComplete] =
    React.useState(false);

  const [position, setPosition] = useState(idealPosition);
  const positionsToAttempt = useMemo(
    () =>
      allowOutsideWindow
        ? []
        : [
            ...new Set([
              ...positionSwapPriorities[idealPosition],
              ...Object.values(FloatingWithParentPosition).filter(
                pos => pos !== idealPosition
              )
            ])
          ],
    [allowOutsideWindow, idealPosition]
  );
  const [nextPositionToAttemptIndex, setNextPositionAttemptIndex] = useState(0);

  const [minWidth, setMinWidth] = useState<number | undefined>(undefined);

  const uniqueAnchorName = useMemo(() => {
    if (!anchorName || anchorName === "") {
      return nanoid();
    }
    return anchorName;
  }, [anchorName]);

  useEffect(() => {
    if (!parentRef?.current || !uniqueAnchorName) {
      return;
    }
    parentRef.current.style.position = "relative";
    parentRef.current.style.setProperty("anchor-name", `--${uniqueAnchorName}`);
  }, [parentRef, anchorName, uniqueAnchorName]);

  const checkFloatingPosition = useCallback(() => {
    const parentPosition = parentRef?.current?.getBoundingClientRect();
    if (!parentPosition) {
      setMinWidth(undefined);
      return {};
    }

    if (minWidthAsParent) {
      setMinWidth(parentPosition.width);
    }

    if (
      !floatingWithParentRef.current ||
      nextPositionToAttemptIndex > positionsToAttempt.length
    ) {
      return;
    }

    const floatingWithParentPosition =
      floatingWithParentRef?.current?.getBoundingClientRect();

    const floatingWithParentPositionWidth = minWidthAsParent
      ? parentPosition.width
      : floatingWithParentPosition.width;

    const positionedOutsideOfWindow =
      floatingWithParentPosition?.top < 0 ||
      floatingWithParentPosition?.y + floatingWithParentPosition?.height >
        windowHeight ||
      floatingWithParentPosition?.left < 0 ||
      floatingWithParentPosition?.x + floatingWithParentPositionWidth >
        windowWidth;

    console.log(
      "FloatingWithParent position check",
      positionedOutsideOfWindow,
      {
        top: floatingWithParentPosition?.top,
        bottom:
          floatingWithParentPosition?.y + floatingWithParentPosition?.height,
        left: floatingWithParentPosition?.left,
        right: floatingWithParentPosition?.x + floatingWithParentPositionWidth
      }
    );
    if (positionedOutsideOfWindow) {
      setPosition(() => {
        if (
          allowOutsideWindow ||
          nextPositionToAttemptIndex >= positionsToAttempt.length
        ) {
          // Doesn't fit anywhere, so just return the ideal position
          return idealPosition;
        }
        return positionsToAttempt[nextPositionToAttemptIndex];
      });

      setNextPositionAttemptIndex(prev => prev + 1);
    }
  }, [
    parentRef,
    minWidthAsParent,
    nextPositionToAttemptIndex,
    positionsToAttempt,
    windowHeight,
    windowWidth,
    allowOutsideWindow,
    idealPosition
  ]);

  useEffect(() => {
    checkFloatingPosition();
    setInitialCalculationComplete(true);

    const resetAndCheckFloatingPosition = () => {
      setNextPositionAttemptIndex(0);
      checkFloatingPosition();
      setPosition(idealPosition);
    };

    if (parentRef?.current) {
      window.addEventListener("resize", resetAndCheckFloatingPosition);
      window.addEventListener("mousewheel", resetAndCheckFloatingPosition);
      return function () {
        window.removeEventListener("mousewheel", resetAndCheckFloatingPosition);
        window.removeEventListener("resize", resetAndCheckFloatingPosition);
      };
    }
  }, [
    checkFloatingPosition,
    parentRef,
    initialCalculationComplete,
    className,
    idealPosition
  ]);

  return (
    <Box
      classNames={composeComponentClassNames(
        "floating-with-parent",
        {
          position,
          minWidthAsParent
        },
        [
          className ?? "",
          returnStringIfTrue(skipGap, "floating-with-parent--skip-gap")
        ]
      )}
      style={{
        zIndex,
        ["positionAnchor" as string]: `--${uniqueAnchorName}`,
        minWidth,
        pointerEvents: "auto" // Add this
      }}
      ref={floatingWithParentRef}
    >
      {children}
    </Box>
  );
};

FloatingWithParent.displayName = "FloatingWithParent";
