.floating-with-parent {
  z-index: 50;
  position: fixed;

  pointer-events: auto; // Ensure clicks are caught

  &::before {
    content: "";
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none; // Allow clicks through overlay
    background: transparent;
    z-index: -1;
  }

  // Ensure all children catch events
  & > * {
    pointer-events: auto;
  }

  &--position-left {
    margin-right: var(--components-floating-with-parent-offset, 4px);
    right: anchor(left);
    top: anchor(top);
  }

  &--position-right {
    margin-left: var(--components-floating-with-parent-offset, 4px);
    left: anchor(right);
    top: anchor(top);
  }

  &--position-top-left {
    bottom: anchor(top);
    margin-bottom: var(--components-floating-with-parent-offset, 4px);
    left: anchor(start);
  }

  &--position-top-right {
    bottom: anchor(top);
    margin-bottom: var(--components-floating-with-parent-offset, 4px);
    right: anchor(end);
  }

  &--position-bottom-left {
    top: anchor(bottom);
    margin-top: var(--components-floating-with-parent-offset, 4px);
    left: anchor(start);
  }

  &--position-bottom-right {
    top: anchor(bottom);
    margin-top: var(--components-floating-with-parent-offset, 4px);
    right: anchor(end);
  }

  &--position-left-center {
    margin-right: var(--components-floating-with-parent-offset, 4px);
    right: anchor(left);
    top: anchor(center);
  }

  &--position-right-center {
    margin-left: var(--components-floating-with-parent-offset, 4px);
    left: anchor(right);
    top: anchor(center);
  }

  &--position-left-bottom {
    margin-right: var(--components-floating-with-parent-offset, 4px);
    right: anchor(left);
    bottom: anchor(end);
  }

  &--position-right-bottom {
    margin-left: var(--components-floating-with-parent-offset, 4px);
    left: anchor(right);
    bottom: anchor(end);
  }

  &--minWidthAsParent-true {
    min-width: inherit;
  }

  &--skip-gap {
    margin-left: 0px;
    margin-right: 0px;
    margin-top: 0px;
    margin-bottom: 0px;
  }
}
