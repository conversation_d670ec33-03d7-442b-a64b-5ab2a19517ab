import { useCallback } from "react";

import { useTableFieldContext } from "../TableFieldContext";
import {
  TableFieldCellLocation,
  TableFieldCellType,
  TableFieldCellValue,
  TableFieldColumn,
  TableFieldSetCell,
  TableFieldSetColumn,
  TableFieldSetRow,
  TableFieldSetTable,
  TableFieldValue
} from "../TableFieldTypes";

export const useTableFieldCellHandlers = ({
  setCell,
  setRow,
  setColumn,
  setTable,
  refreshTableStylingCalculations,
  value
}: {
  setCell?: TableFieldSetCell;
  setColumn?: TableFieldSetColumn;
  setRow?: TableFieldSetRow;
  setTable?: TableFieldSetTable;
  refreshTableStylingCalculations: () => void;
  value: TableFieldValue;
}) => {
  const {
    tableFieldId,
    setHighlightedCells,
    columns,
    disabled,
    focusedCell,
    setFocusedCell
  } = useTableFieldContext();

  const handleCellOnPaste = useCallback(
    (e: React.ClipboardEvent, cell: TableFieldCellLocation) => {
      if (disabled) {
        return;
      }

      // Split as rows / columns
      const clipboardData = e.clipboardData.getData("text");
      const rows = clipboardData.split("\n");
      const pastedColumns = rows.map(row => row.split("\t"));

      const isUpdateRowOnly = pastedColumns.length === 1;
      const isUpdateColumnOnly = pastedColumns.every(row => row.length === 1);
      const isUpdateSingleCellOnly = isUpdateRowOnly && isUpdateColumnOnly;

      if (isUpdateSingleCellOnly) {
        setCell?.(
          getValueAsType(pastedColumns[0][0], columns[cell.columnIndex]),
          cell.rowIndex,
          columns[cell.columnIndex].id
        );
        setHighlightedCells?.({
          from: cell,
          to: {
            rowIndex: cell.rowIndex + pastedColumns.length - 1,
            columnIndex: cell.columnIndex
          }
        });
        refreshTableStylingCalculations();
        return;
      }

      e.stopPropagation();
      e.preventDefault();

      if (isUpdateColumnOnly) {
        setColumn?.(
          pastedColumns.map(row =>
            getValueAsType(row[0], columns[cell.columnIndex])
          ) as TableFieldCellValue[],
          columns[cell.columnIndex].id,
          {
            fromRowIndex: cell.rowIndex,
            keepOtherColumnValues: true
          }
        );
        setHighlightedCells?.({
          from: cell,
          to: {
            rowIndex: cell.rowIndex + pastedColumns.length - 1,
            columnIndex: cell.columnIndex
          }
        });
        refreshTableStylingCalculations();
        return;
      } else if (isUpdateRowOnly) {
        const updatedRow = pastedColumns[0].reduce((acc, value, index) => {
          const column = columns[index + cell.columnIndex];
          if (!column) {
            return acc;
          }
          const valueAsType = getValueAsType(value, column);
          return {
            ...acc,
            [columns[index + cell.columnIndex].id]: valueAsType
          };
        }, {});
        setRow?.(updatedRow, cell.rowIndex, {
          newRow: !value.length || cell.rowIndex === value.length,
          keepOtherRowValues: true
        });
        setHighlightedCells?.({
          from: cell,
          to: {
            rowIndex: cell.rowIndex,
            columnIndex: cell.columnIndex + pastedColumns[0].length - 1
          }
        });
        refreshTableStylingCalculations();
        return;
      }

      const updatedValue = pastedColumns.map(row =>
        row.reduce((acc, value, columnIndex) => {
          const column = columns[columnIndex + cell.columnIndex];
          if (!column) {
            return acc;
          }
          const valueAsType = getValueAsType(value, column);
          return {
            ...acc,
            [column.id]: valueAsType
          };
        }, {})
      );

      setTable?.(updatedValue, {
        fromRowIndex: cell.rowIndex
      });
      setHighlightedCells?.({
        from: cell,
        to: {
          rowIndex: cell.rowIndex + pastedColumns.length - 1,
          columnIndex: cell.columnIndex + pastedColumns[0].length - 1
        }
      });
      refreshTableStylingCalculations();
    },
    [
      columns,
      disabled,
      refreshTableStylingCalculations,
      setColumn,
      setHighlightedCells,
      setRow,
      setTable,
      setCell,
      value.length
    ]
  );

  const handleCellOnKeyDown = useCallback(
    (
      e: React.KeyboardEvent,
      { rowIndex, columnIndex }: TableFieldCellLocation
    ) => {
      const currentFocusedColumn =
        focusedCell && focusedCell?.columnIndex !== undefined
          ? columns[focusedCell.columnIndex]
          : undefined;
      if (!currentFocusedColumn) {
        return;
      }
      let updatedFocusCell: TableFieldCellLocation | undefined;

      const focusedColumnTypeBlocksUpDown = [
        TableFieldCellType.BOOLEAN,
        TableFieldCellType.SELECT,
        TableFieldCellType.MULTI_SELECT
      ] as string[];

      const currentTarget = e.currentTarget as unknown as {
        selectionStart: number;
        selectionEnd: number;
        value: string;
      };

      if (e.key === "ArrowUp") {
        let canArrowUp = !focusedColumnTypeBlocksUpDown.includes(
          currentFocusedColumn.type
        );
        if (!canArrowUp) {
          const focusedCellElement = document.getElementById(
            `${tableFieldId}-cell-${rowIndex}-${columnIndex}`
          );
          if (
            focusedCellElement &&
            !focusedCellElement.querySelector(".floating-with-parent")
          ) {
            canArrowUp = true;
          }
        }
        if (canArrowUp || e.shiftKey) {
          const nextRowIndex = Math.max(0, rowIndex - 1);
          updatedFocusCell = { rowIndex: nextRowIndex, columnIndex };
        }
      } else if (e.key === "ArrowDown") {
        let canArrowDown = !focusedColumnTypeBlocksUpDown.includes(
          currentFocusedColumn.type
        );
        if (!canArrowDown) {
          const focusedCellElement = document.getElementById(
            `${tableFieldId}-cell-${rowIndex}-${columnIndex}`
          );
          if (focusedCellElement) {
            const floatingWithParent = focusedCellElement.querySelector(
              ".floating-with-parent"
            );
            if (!floatingWithParent) {
              canArrowDown = true;
            }
          }
        }
        if (canArrowDown || e.shiftKey) {
          const nextRowIndex = Math.min(value.length, rowIndex + 1);
          updatedFocusCell = { rowIndex: nextRowIndex, columnIndex };
        }
      } else if (e.key === "ArrowLeft") {
        // check the cursor is at the beginning of the text
        if (
          currentFocusedColumn.type === "boolean" ||
          e.shiftKey ||
          !currentTarget.selectionStart ||
          currentTarget.selectionStart === 0
        ) {
          const nextColumnIndex = Math.max(0, columnIndex - 1);
          updatedFocusCell = { rowIndex, columnIndex: nextColumnIndex };
        }
      } else if (e.key === "ArrowRight") {
        if (
          currentFocusedColumn.type === "boolean" ||
          e.shiftKey ||
          !currentTarget.selectionEnd ||
          currentTarget.selectionEnd === currentTarget.value.length
        ) {
          const nextColumnIndex = Math.min(columns.length - 1, columnIndex + 1);
          updatedFocusCell = { rowIndex, columnIndex: nextColumnIndex };
        }
      }

      if (
        updatedFocusCell &&
        (updatedFocusCell.rowIndex !== focusedCell.rowIndex ||
          updatedFocusCell.columnIndex !== focusedCell.columnIndex)
      ) {
        (document.activeElement as HTMLInputElement)?.blur();
        setFocusedCell(updatedFocusCell);
        e.stopPropagation();
        e.preventDefault();
      }
    },
    [columns, focusedCell, setFocusedCell, tableFieldId, value.length]
  );

  return { handleCellOnPaste, handleCellOnKeyDown };
};

function getValueAsType(value: string, column: TableFieldColumn) {
  if (column.customGetValue) {
    return column.customGetValue(value);
  }
  if (column.type === TableFieldCellType.TEXT) {
    return value;
  } else if (column.type === TableFieldCellType.NUMBER) {
    const resolved = parseInt(value);
    if (isNaN(resolved)) {
      return undefined;
    }
    return resolved.toString();
  } else if (column.type === TableFieldCellType.BOOLEAN) {
    const lowerCaseValue = value.toLowerCase();
    if (lowerCaseValue == "true" || lowerCaseValue == "yes" || value == "1") {
      return true;
    } else if (
      lowerCaseValue == "false" ||
      lowerCaseValue == "no" ||
      value == "0"
    ) {
      return false;
    } else {
      return undefined;
    }
  } else if (column.type === TableFieldCellType.SELECT) {
    const option = column.properties?.options?.find(
      option => option.label === value || option.value === value
    );
    return option?.value ?? "";
  } else if (column.type === TableFieldCellType.MULTI_SELECT) {
    const options = column.properties?.options?.filter(option =>
      value.split(",").includes(option.label)
    );
    return options?.map(option => option.value);
  } else if (column.type === TableFieldCellType.LIST) {
    try {
      const listAnswer = typeof value === "string" ? JSON.parse(value) : value;

      if (!listAnswer?.order || !listAnswer?.entities) return [];

      return listAnswer.order.map((id: string) => {
        const entity = listAnswer.entities[id];
        if (!entity?.item) return "";
        const questionId = Object.keys(entity.item)[0];
        return entity.item[questionId]?.value ?? "";
      });
    } catch (error) {
      console.warn("Failed to parse list answer:", error);
      return [];
    }
  }
  return value;
}
