import { useCallback, useEffect, useState } from "react";

import { useTableFieldContext } from "../TableFieldContext";
import { TableFieldCellLocation } from "../TableFieldTypes";

export const useTableFieldHighlightHandler = (
  tableRef: React.RefObject<HTMLTableElement>
) => {
  const { focusedCell, setHighlightedCells } = useTableFieldContext();

  const [isMouseDown, setIsMouseDown] = useState(false);
  const [startDragCell, setStartDragCell] = useState<
    TableFieldCellLocation | undefined
  >(undefined);
  const [prevDragCell, setPrevDragCell] = useState<
    TableFieldCellLocation | undefined
  >(undefined);

  useEffect(() => {
    if (focusedCell.columnIndex === -1 || focusedCell.rowIndex === -1) {
      setHighlightedCells(undefined);
    }
  }, [focusedCell, setHighlightedCells]);

  const handleDragOverCell = useCallback(
    (cell: TableFieldCellLocation) => {
      if (!isMouseDown || startDragCell === undefined) {
        return;
      }
      const { columnIndex: currentColumnIndex, rowIndex: currentRowIndex } =
        cell;

      if (
        prevDragCell?.columnIndex === currentColumnIndex &&
        prevDragCell?.rowIndex === currentRowIndex
      ) {
        return;
      }

      // Detect if moving up or down
      const currentCellDraggingOver = {
        rowIndex: currentRowIndex,
        columnIndex: currentColumnIndex
      };
      setPrevDragCell(currentCellDraggingOver);

      const startingRow = Math.min(
        startDragCell?.rowIndex ?? 0,
        currentCellDraggingOver.rowIndex
      );
      const startingColumn = Math.min(
        startDragCell?.columnIndex ?? 0,
        currentCellDraggingOver.columnIndex
      );
      const endingRow = Math.max(
        startDragCell?.rowIndex ?? 0,
        currentCellDraggingOver.rowIndex
      );
      const endingColumn = Math.max(
        startDragCell?.columnIndex ?? 0,
        currentCellDraggingOver.columnIndex
      );
      setHighlightedCells({
        from: {
          rowIndex: startingRow,
          columnIndex: startingColumn
        },
        to: {
          rowIndex: endingRow,
          columnIndex: endingColumn
        }
      });
    },
    [
      isMouseDown,
      prevDragCell?.columnIndex,
      prevDragCell?.rowIndex,
      setHighlightedCells,
      startDragCell
    ]
  );

  useEffect(() => {
    if (!tableRef.current) {
      return;
    }
    const ref = tableRef.current;
    const handleMouseDown = () => {
      setIsMouseDown(true);
    };
    const handleMouseUp = () => {
      setIsMouseDown(false);
      setStartDragCell(undefined);
      setPrevDragCell(undefined);
    };
    ref.addEventListener("mousedown", handleMouseDown);
    ref.addEventListener("mouseup", handleMouseUp);
    return () => {
      ref.removeEventListener("mousedown", handleMouseDown);
      ref.removeEventListener("mouseup", handleMouseUp);
    };
  }, [setHighlightedCells, tableRef]);

  return {
    setStartDragCell: (cell: TableFieldCellLocation | undefined) => {
      setStartDragCell(cell);
      setPrevDragCell(undefined);
      if (cell !== undefined) {
        setHighlightedCells(undefined);
      }
    },
    handleDragOverCell
  };
};
