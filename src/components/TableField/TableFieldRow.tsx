import React, { useMemo } from "react";

import { Box, Inline } from "../../fermions";
import { getClassNames, returnStringIfTrue } from "../../helpers";
import { Label } from "../Label";
import { TableFieldCell } from "./TableFieldCell";
import { useTableFieldContext } from "./TableFieldContext";
import {
  TableFieldCellLocation,
  TableFieldCellValue,
  TableFieldRowValue
} from "./TableFieldTypes";

export const TableFieldRow = ({
  rowValues,
  rowIndex,
  onChange,
  onlyTriggerChangeWhenBlur,
  handleOnKeyDown,
  columnStyling,
  isMockRow,
  onPaste,
  rowActions,
  setStartDragCell,
  handleDragOverCell,
  shouldRenderRow
}: {
  rowValues?: TableFieldRowValue;
  rowIndex: number;
  onChange?: (value: TableFieldCellValue | undefined, columnId: string) => void;
  onlyTriggerChangeWhenBlur?: boolean;
  handleOnKeyDown?: (
    e: React.KeyboardEvent,
    cell: TableFieldCellLocation
  ) => void;
  columnStyling: React.CSSProperties[];
  isMockRow?: boolean;
  onPaste?: (e: React.ClipboardEvent, cell: TableFieldCellLocation) => void;
  rowActions?: () => React.ReactNode;
  setStartDragCell: (cell: TableFieldCellLocation | undefined) => void;
  handleDragOverCell: (cell: TableFieldCellLocation) => void;
  shouldRenderRow?: boolean;
}) => {
  const {
    tableFieldId,
    columns,
    disabled,
    focusedCell,
    setFocusedCell,
    stickyColumnIndex,
    highlightedCells,
    setHighlightedCells,
    translations
  } = useTableFieldContext();

  const rowContainsHighlightedCells = useMemo(
    () =>
      highlightedCells?.from &&
      highlightedCells.to &&
      rowIndex >= highlightedCells.from?.rowIndex &&
      rowIndex <= highlightedCells.to?.rowIndex,
    [highlightedCells, rowIndex]
  );

  return (
    <tr
      id={`${tableFieldId}-row-${rowIndex}`}
      data-row-index={rowIndex}
      className={getClassNames([
        "table-field__row",
        returnStringIfTrue(
          focusedCell?.rowIndex === rowIndex,
          "table-field__row--focused"
        ),
        `table-field__row--${rowContainsHighlightedCells ? "contains" : "does-not-contain"}-highlighted-cells`
      ])}
      key={rowIndex}
      style={!shouldRenderRow ? { height: "37px" } : {}}
    >
      <td
        id={`${tableFieldId}-row-${rowIndex}-column-counter`}
        className={getClassNames([
          "table-field__data",
          "table-field__cell",
          "table-field__cell--sticky",
          "table-field__cell--counter",
          "table-field__cell--not-highlighted"
        ])}
      >
        <Inline
          className="table-field__cell__content"
          width="100"
          height="100"
          alignment="center"
        >
          <Label
            label={isMockRow ? "" : String(rowIndex + 1)}
            disabled={disabled}
          />
        </Inline>
        {rowActions?.()}
        <Box className="table-field__cell__styler">
          <Box className="table-field__cell__styler" />
        </Box>
      </td>
      {columns.map((column, columnIndex) => {
        const isHighlighted =
          highlightedCells &&
          rowIndex >= highlightedCells.from?.rowIndex &&
          rowIndex <= highlightedCells.to?.rowIndex &&
          columnIndex >= highlightedCells.from?.columnIndex &&
          columnIndex <= highlightedCells.to?.columnIndex;
        const isSticky =
          stickyColumnIndex !== undefined && columnIndex <= stickyColumnIndex;

        if (!shouldRenderRow) {
          return (
            <td
              className={getClassNames([
                "table-field__data",
                returnStringIfTrue(isSticky, "table-field__cell--sticky"),
                "table-field__cell",
                `table-field__cell--${isHighlighted ? "highlighted" : "not-highlighted"}`
              ])}
            >
              <Box
                className="table-field__cell__styler"
                style={{
                  backgroundColor: "var(--color-surface-primary-overlay)",
                  border: "0px solid transparent"
                }}
              />
            </td>
          );
        }
        return (
          <td
            data-pos_id={`${rowIndex}-${columnIndex}`}
            id={`${tableFieldId}-cell-${rowIndex}-${columnIndex}`}
            key={column.id}
            style={{
              ...(columnStyling[columnIndex] ?? []),
              minWidth: "auto",
              maxWidth: "auto"
            }}
            className={getClassNames([
              "table-field__data",
              returnStringIfTrue(isSticky, "table-field__cell--sticky"),
              "table-field__cell",
              `table-field__cell--${isHighlighted ? "highlighted" : "not-highlighted"}`
            ])}
            onClick={() => {
              setFocusedCell?.({ rowIndex, columnIndex });
              setHighlightedCells?.(undefined);
            }}
            draggable={true}
            onDragStart={() => {
              setStartDragCell({
                rowIndex,
                columnIndex
              });
            }}
            onDragEnd={() => {
              setStartDragCell(undefined);
            }}
            onDragOver={() => {
              handleDragOverCell({
                rowIndex,
                columnIndex
              });
            }}
          >
            <Inline className="table-field__cell__content" alignment="left">
              <TableFieldCell
                column={column}
                value={rowValues?.[column.id]}
                onChange={value => onChange?.(value, column.id)}
                focused={
                  focusedCell?.rowIndex === rowIndex &&
                  focusedCell?.columnIndex === columnIndex
                }
                onFocus={() => setFocusedCell?.({ rowIndex, columnIndex })}
                onBlur={() =>
                  setFocusedCell?.({ rowIndex: -1, columnIndex: -1 })
                }
                onPaste={e => onPaste?.(e, { rowIndex, columnIndex })}
                onlyTriggerChangeWhenBlur={onlyTriggerChangeWhenBlur}
                onKeyDown={e => handleOnKeyDown?.(e, { rowIndex, columnIndex })}
                disabled={disabled}
                translations={translations}
                rowIndex={rowIndex}
              />
            </Inline>
            <Box className="table-field__cell__styler">
              {columnIndex === stickyColumnIndex && (
                <Box className="table-field__cell__styler__shadow" />
              )}
            </Box>
          </td>
        );
      })}
    </tr>
  );
};
