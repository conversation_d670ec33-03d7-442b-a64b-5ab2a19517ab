import React, { useEffect, useState } from "react";

import {
  TableFieldCellValue,
  TableFieldColumn,
  TableFieldTranslations
} from "./TableFieldTypes";

export const TableFieldCell = ({
  column,
  value,
  onChange,
  focused,
  onFocus,
  onBlur,
  onPaste,
  onlyTriggerChangeWhenBlur,
  onKeyDown,
  disabled,
  translations,
  rowIndex
}: {
  column: TableFieldColumn;
  value?: TableFieldCellValue;
  onChange?: (value: TableFieldCellValue) => void;
  focused?: boolean;
  onFocus?: React.FocusEventHandler;
  onBlur?: React.FocusEventHandler;
  onPaste?: React.ClipboardEventHandler;
  onlyTriggerChangeWhenBlur?: boolean;
  onKeyDown?: React.KeyboardEventHandler;
  disabled?: boolean;
  translations: TableFieldTranslations;
  rowIndex: number;
}) => {
  const [controlFocus, setControlFocus] = useState<boolean | undefined>(
    undefined
  );

  useEffect(() => {
    if (focused === true) {
      setControlFocus(true);
      setTimeout(() => {
        setControlFocus(undefined);
      });
    }
  }, [focused]);

  if (column.customCellRenderer) {
    return column.customCellRenderer({
      value,
      onChange,
      autoFocus: focused,
      onFocus,
      onPaste,
      onBlur,
      onKeyDown,
      onlyTriggerChangeWhenBlur,
      disabled: disabled || column.properties?.disabled,
      controlFocus,
      translations,
      rowIndex
    });
  }

  return <>Type not yet supported</>;
};
