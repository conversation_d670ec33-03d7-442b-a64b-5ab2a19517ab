import React, {
  InputHTMLAttributes,
  useCallback,
  useEffect,
  useMemo
} from "react";

import { debounce } from "lodash";

import { Box, Inline } from "../../fermions/index.ts";
import { CommonInputProps } from "../../helpers/componentHelpers.ts";
import { useWarningCheckForInputs } from "../../hooks/useWarningCheckForInputs.tsx";
import { ColorText } from "../../tokens/IonsInterface.ts";
import { CloseIconButton, IconButton } from "../Button/index.ts";
import { IconSize } from "../Icon/IconTypes.ts";
import { Label } from "../Label/Label.tsx";
import { LabelTextPosition } from "../Label/LabelTypes.ts";
import "./TextField.scss";
import { TextFieldType } from "./TextFieldTypes.ts";

export interface TextFieldProps extends CommonInputProps {
  // Controlled
  value?: string;
  onChange?: (value: string) => void;
  valueSequenceId?: number; // use this to force re-render

  // Uncontrolled
  defaultValue?: string;

  type?: `${TextFieldType}`;
  minLength?: InputHTMLAttributes<HTMLInputElement>["minLength"];
  maxLength?: InputHTMLAttributes<HTMLInputElement>["maxLength"];

  regex?: RegExp;

  withDebounce?: boolean;
  debounceTime?: number;
  autoComplete?: string;
  onlyTriggerChangeWhenBlur?: boolean;
  allowClear?: boolean;
  controlFocus?: boolean;
  leftElement?: React.ReactNode;
  userCanReveal?: boolean;
}

export const TextField = ({
  name,
  id,
  label = "",
  value,
  onChange,
  defaultValue,
  placeholder,
  description,
  tooltip,
  required,
  hidden,
  error,
  disabled,
  autoFocus = false,
  type = TextFieldType.TEXT,
  width = "fit",
  minLength,
  maxLength,
  regex,
  withDebounce,
  debounceTime = 300,
  className = "",
  autoComplete = type === TextFieldType.PASSWORD ? "current-password" : "off",
  onlyTriggerChangeWhenBlur = false,
  onClick,
  onFocus,
  onBlur,
  onKeyDown,
  onKeyUp,
  onPaste,
  allowClear = !required,
  valueSequenceId,
  leftElement,
  userCanReveal = type === TextFieldType.PASSWORD,
  controlFocus
}: TextFieldProps) => {
  const inputRef = React.useRef<HTMLInputElement>(null);
  useWarningCheckForInputs({ value, defaultValue, onChange });
  const [tempValue, setTempValue] = React.useState(
    value ? value : defaultValue
  );
  const [tempType, setTempType] = React.useState(type);

  useEffect(() => {
    setTempType(type);
  }, [type]);

  const debounceCalculate = useMemo(
    () => (onChange ? debounce(onChange, debounceTime) : () => {}),
    [debounceTime, onChange]
  );

  useEffect(() => {
    if (controlFocus === true && document.activeElement !== inputRef.current) {
      inputRef.current?.focus();
    } else if (
      controlFocus === false &&
      document.activeElement === inputRef.current
    ) {
      inputRef.current?.blur();
    }
  }, [controlFocus]);

  const handleChangeInternally = useCallback(
    (
      updatedValue: string,
      changeWithDebounce = withDebounce,
      notifyExternalChange = !onlyTriggerChangeWhenBlur
    ) => {
      if (updatedValue && regex && !regex.test(updatedValue)) {
        return;
      }

      setTempValue(updatedValue);
      if (!onChange || !notifyExternalChange) {
        return;
      }

      if (updatedValue == value) {
        return;
      }
      if (changeWithDebounce) {
        debounceCalculate(updatedValue);
      } else {
        onChange(updatedValue);
      }
    },
    [
      value,
      withDebounce,
      onlyTriggerChangeWhenBlur,
      regex,
      onChange,
      debounceCalculate
    ]
  );

  useEffect(() => {
    // The valueSequenceId is used to force re-render the component because there may be cases where in a controlled component
    //  the value get updated to the same value, but differs from tempValue, thus causing a stale value to be displayed
    handleChangeInternally(value ? value : (defaultValue ?? ""), false, false);
  }, [value, valueSequenceId, defaultValue]);

  const inputAttributes: {
    [key: string]: string | number | boolean | undefined;
  } = {
    className: "text-field__input",
    type,
    placeholder,
    name,
    id,
    disabled,
    minLength,
    maxLength,
    autoFocus,
    autoComplete
  };

  return (
    <Box classNames={["text-field", className]} width={width}>
      <Label
        htmlFor={id}
        label={label}
        required={required}
        disabled={disabled}
        hidden={hidden}
        textPosition={LabelTextPosition.TOP}
        description={description}
        tooltip={tooltip}
        error={error}
      >
        <Inline className="text-field__value">
          {leftElement}
          <input
            ref={inputRef}
            {...inputAttributes}
            value={tempValue}
            onChange={e => {
              handleChangeInternally?.(e.target.value);
            }}
            onBlur={e => {
              onBlur?.(e);
              if (onlyTriggerChangeWhenBlur) {
                handleChangeInternally?.(e.target.value, false, true);
              }
            }}
            data-1p-ignore={autoComplete === "off"}
            onKeyDown={e => {
              onKeyDown?.(e);
              if (e.key === "Enter") {
                e.currentTarget.blur();
              }
            }}
            onFocus={onFocus}
            onKeyUp={onKeyUp}
            onClick={onClick}
            onPaste={onPaste}
            type={tempType}
          />
          <Inline className="text-field__right" gap="050" alignment="right">
            {allowClear &&
              !disabled &&
              (tempValue === undefined ? !!defaultValue : !!tempValue) && (
                <CloseIconButton
                  className="text-field__clear"
                  onClick={e => {
                    handleChangeInternally("", false, true);
                    e.stopPropagation();
                    e.preventDefault();
                  }}
                  skipFocus
                  color={ColorText.PRIMARY}
                  size={IconSize.SMALL}
                  disabled={disabled}
                />
              )}
            {type === TextFieldType.PASSWORD && userCanReveal && (
              <IconButton
                name={tempType === "password" ? "visibility_off" : "visibility"}
                onClick={() => {
                  setTempType(
                    tempType === TextFieldType.PASSWORD
                      ? TextFieldType.TEXT
                      : TextFieldType.PASSWORD
                  );
                }}
                color={ColorText.TERTIARY}
                fillStyle="filled"
              />
            )}
          </Inline>
        </Inline>
      </Label>
    </Box>
  );
};

TextField.displayName = "TextField";
