import {
  FlowConfiguration,
  MockFlowContext
} from "@src/types/FlowConfiguration/FlowConfiguration";
import { ExtendedQuestionTypes, QuestionTypes } from "@src/types/Question";

interface WorkspaceScopeConfig {
  type: string;
  identifier: string;
  path: string;
  valueType: QuestionTypes | ExtendedQuestionTypes;
}

export const WorkspaceScopeNamespace = "_workspace_";

export enum WorkspaceScopeType {
  WORKSPACE_CONFIGURATION = "workspaceConfiguration",
  WORKSPACE_VARIABLES = "workspaceVariables"
}

export const WORKSPACE_SCOPE_CONFIG: Record<string, WorkspaceScopeConfig> = {
  documentId: {
    type: WorkspaceScopeType.WORKSPACE_CONFIGURATION,
    identifier: "DocumentId",
    path: `${WorkspaceScopeNamespace}.documentId`,
    valueType: QuestionTypes.TEXT
  },
  workspaceId: {
    type: WorkspaceScopeType.WORKSPACE_CONFIGURATION,
    identifier: "WorkspaceId",
    path: `${WorkspaceScopeNamespace}.id`,
    valueType: QuestionTypes.NUMBER
  },
  workspaceKey: {
    type: WorkspaceScopeType.WORKSPACE_CONFIGURATION,
    identifier: "WorkspaceKey",
    path: `${WorkspaceScopeNamespace}.key`,
    valueType: QuestionTypes.TEXT
  },
  workspaceName: {
    type: WorkspaceScopeType.WORKSPACE_CONFIGURATION,
    identifier: "WorkspaceName",
    path: `${WorkspaceScopeNamespace}.name`,
    valueType: QuestionTypes.TEXT
  },
  workspaceFoundationId: {
    type: WorkspaceScopeType.WORKSPACE_CONFIGURATION,
    identifier: "WorkspaceFoundationId",
    path: `${WorkspaceScopeNamespace}.workspaceFoundationId`,
    valueType: QuestionTypes.NUMBER
  },
  variables: {
    type: WorkspaceScopeType.WORKSPACE_VARIABLES,
    identifier: "WorkspaceVariables",
    path: `${WorkspaceScopeNamespace}.variables`,
    valueType: ExtendedQuestionTypes.WORKSPACE_VARIABLES
  }
};

export const getWorkspaceScope = ({
  flowContext,
  configurationFlow
}: {
  flowContext?: MockFlowContext;
  configurationFlow: FlowConfiguration;
}) => {
  if (flowContext) {
    const variables = Object.entries(WORKSPACE_SCOPE_CONFIG).reduce(
      (acc, [key, config]) => ({
        ...acc,
        [`${WorkspaceScopeNamespace}.${key}`]: {
          ...config,
          availableFromStepId: configurationFlow.start
        }
      }),
      {}
    );

    flowContext.variables = {
      ...flowContext.variables,
      ...variables
    };
  }
};

export const getWorkspaceScopeByIdentifier = (identifier: string) => {
  return Object.values(WORKSPACE_SCOPE_CONFIG).find(
    config => config.identifier === identifier
  );
};
